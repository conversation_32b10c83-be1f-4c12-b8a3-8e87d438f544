// This file is automatically generated. Do not edit it directly.
import { createClient } from '@supabase/supabase-js';
import type { Database } from './types';

const SUPABASE_URL = "https://jgidblaeongxfjavsnhy.supabase.co";
const SUPABASE_PUBLISHABLE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImpnaWRibGFlb25neGZqYXZzbmh5Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDg2MTQ2MTksImV4cCI6MjA2NDE5MDYxOX0.RG455r7d1rSeY9V9cobz72caWQXHd5w20_FEpQrZHAA";

// Import the supabase client like this:
// import { supabase } from "@/integrations/supabase/client";

export const supabase = createClient<Database>(SUPABASE_URL, SUPABASE_PUBLISHABLE_KEY);