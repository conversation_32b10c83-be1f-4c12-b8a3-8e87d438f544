
import React, { useState } from 'react';
import { Ta<PERSON>, Ta<PERSON>Content, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { FileText, Video, Copy } from 'lucide-react';
import { But<PERSON> } from '@/components/ui/button';
import EditableResult from './EditableResult';
import VersionNavigation from './VersionNavigation';

interface MultiContentTabsProps {
  selectedTypes: string[];
  copywritingContent: string;
  videoScriptContent: string;
  isGenerating: boolean;
  copywritingIndex: number;
  copywritingTotal: number;
  videoScriptIndex: number;
  videoScriptTotal: number;
  onCopywritingPrevious: () => void;
  onCopywritingNext: () => void;
  onVideoScriptPrevious: () => void;
  onVideoScriptNext: () => void;
  onCopywritingChange: (content: string) => void;
  onVideoScriptChange: (content: string) => void;
  onCopyCopywriting: () => void;
  onCopyVideoScript: () => void;
}

const MultiContentTabs: React.FC<MultiContentTabsProps> = ({
  selectedTypes,
  copywritingContent,
  videoScriptContent,
  isGenerating,
  copywritingIndex,
  copywritingTotal,
  videoScriptIndex,
  videoScriptTotal,
  onCopywritingPrevious,
  onCopywritingNext,
  onVideoScriptPrevious,
  onVideoScriptNext,
  onCopywritingChange,
  onVideoScriptChange,
  onCopyCopywriting,
  onCopyVideoScript
}) => {
  const [activeTab, setActiveTab] = useState(selectedTypes[0] || 'copywriting');

  return (
    <div className="bg-gradient-to-br from-gray-50/80 to-sky-50/30 border border-gray-200/70 rounded-2xl p-6 min-h-[320px] backdrop-blur-sm">
      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center space-x-4">
            <TabsList className="grid grid-cols-2 gap-1">
              {selectedTypes.includes('copywriting') && (
                <TabsTrigger 
                  value="copywriting" 
                  className="flex items-center space-x-2"
                >
                  <FileText className="w-4 h-4" />
                  <span>文案</span>
                </TabsTrigger>
              )}
              {selectedTypes.includes('video-script') && (
                <TabsTrigger 
                  value="video-script" 
                  className="flex items-center space-x-2"
                >
                  <Video className="w-4 h-4" />
                  <span>短视频脚本</span>
                </TabsTrigger>
              )}
            </TabsList>

            <VersionNavigation
              currentIndex={activeTab === 'copywriting' ? copywritingIndex : videoScriptIndex}
              totalCount={activeTab === 'copywriting' ? copywritingTotal : videoScriptTotal}
              onPrevious={activeTab === 'copywriting' ? onCopywritingPrevious : onVideoScriptPrevious}
              onNext={activeTab === 'copywriting' ? onCopywritingNext : onVideoScriptNext}
            />
          </div>

          {/* Copy button */}
          {activeTab === 'copywriting' && copywritingContent && !isGenerating && (
            <Button
              onClick={onCopyCopywriting}
              variant="ghost"
              size="sm"
              className="h-8 w-8 p-0 hover:bg-gray-100"
              title="复制文案"
            >
              <Copy className="w-4 h-4" />
            </Button>
          )}
          
          {activeTab === 'video-script' && videoScriptContent && !isGenerating && (
            <Button
              onClick={onCopyVideoScript}
              variant="ghost"
              size="sm"
              className="h-8 w-8 p-0 hover:bg-gray-100"
              title="复制短视频脚本"
            >
              <Copy className="w-4 h-4" />
            </Button>
          )}
        </div>

        {selectedTypes.includes('copywriting') && (
          <TabsContent value="copywriting">
            <div className="border border-gray-200/80 rounded-xl p-4 bg-white/50">
              <EditableResult
                content={copywritingContent}
                isGenerating={isGenerating}
                contentType="copywriting"
                onContentChange={onCopywritingChange}
                onCopy={onCopyCopywriting}
                showCopyButton={false}
              />
            </div>
          </TabsContent>
        )}

        {selectedTypes.includes('video-script') && (
          <TabsContent value="video-script">
            <div className="border border-gray-200/80 rounded-xl p-4 bg-white/50">
              <EditableResult
                content={videoScriptContent}
                isGenerating={isGenerating}
                contentType="video-script"
                onContentChange={onVideoScriptChange}
                onCopy={onCopyVideoScript}
                showCopyButton={false}
              />
            </div>
          </TabsContent>
        )}
      </Tabs>
    </div>
  );
};

export default MultiContentTabs;
