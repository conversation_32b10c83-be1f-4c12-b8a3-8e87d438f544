
import { But<PERSON> } from "@/components/ui/button";
import { Scale, ArrowLeft, RefreshCw } from "lucide-react";
import { useNavigate } from "react-router-dom";

interface GenerateHeaderProps {
  currentDomainInfo: any;
  isGenerating: boolean;
  onRegenerate: () => void;
}

const GenerateHeader = ({ currentDomainInfo, isGenerating, onRegenerate }: GenerateHeaderProps) => {
  const navigate = useNavigate();

  return (
    <div className="bg-white border-b border-gray-200 shadow-sm">
      <div className="max-w-6xl mx-auto px-6 py-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <div className="bg-blue-600 p-2 rounded-lg">
              <Scale className="h-6 w-6 text-white" />
            </div>
            <div>
              <h1 className="text-2xl font-bold text-gray-900">生成的内容 (10条)</h1>
              <p className="text-sm text-gray-600">
                {currentDomainInfo.icon} {currentDomainInfo.title} 专业文案
              </p>
            </div>
          </div>
          <div className="flex items-center space-x-4">
            <Button
              onClick={onRegenerate}
              variant="outline"
              className="flex items-center space-x-2"
              disabled={isGenerating}
            >
              <RefreshCw className={`w-4 h-4 ${isGenerating ? 'animate-spin' : ''}`} />
              <span>{isGenerating ? '生成中...' : '重新生成'}</span>
            </Button>
            <button
              onClick={() => navigate("/domains")}
              className="flex items-center space-x-2 text-gray-600 hover:text-gray-900 transition-colors"
            >
              <ArrowLeft className="w-4 h-4" />
              <span>选择其他领域</span>
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default GenerateHeader;
