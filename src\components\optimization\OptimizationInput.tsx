
import React from 'react';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { <PERSON>rk<PERSON>, RefreshCw } from 'lucide-react';
import { useIsMobile } from '@/hooks/use-mobile';
import ContentTypeSelector from './ContentTypeSelector';

interface OptimizationInputProps {
  originalText: string;
  platform: string;
  style: string;
  contentTypes: string[];
  isOptimizing: boolean;
  hasGenerated: boolean;
  onOriginalTextChange: (text: string) => void;
  onPlatformChange: (platform: string) => void;
  onStyleChange: (style: string) => void;
  onContentTypesChange: (types: string[]) => void;
  onOptimize: () => void;
}

const OptimizationInput: React.FC<OptimizationInputProps> = ({
  originalText,
  platform,
  style,
  contentTypes,
  isOptimizing,
  hasGenerated,
  onOriginalTextChange,
  onPlatformChange,
  onStyleChange,
  onContentTypesChange,
  onOptimize,
}) => {
  const isMobile = useIsMobile();

  const handleOptimize = () => {
    if (contentTypes.length === 0) {
      // 如果没有选择类型，默认选择文案
      onContentTypesChange(['copywriting']);
    }
    onOptimize();
  };

  // 根据状态决定按钮文案、图标和样式
  const isRegenerate = hasGenerated && originalText.trim();
  const buttonText = isOptimizing 
    ? "正在生成..." 
    : (isRegenerate ? "重新生成" : "开始生成");
  // 修改图标：开始生成用Sparkles，重新生成用RefreshCw
  const ButtonIcon = isRegenerate ? RefreshCw : Sparkles;
  // 调换颜色：开始生成用蓝色，重新生成用绿色
  const buttonClassName = isRegenerate
    ? "w-full bg-gradient-to-r from-green-500 to-emerald-600 hover:from-green-600 hover:to-emerald-700 text-white py-3 rounded-xl font-medium text-base transition-all duration-200 transform hover:scale-[1.02] shadow-lg"
    : "w-full bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 text-white py-3 rounded-xl font-medium text-base transition-all duration-200 transform hover:scale-[1.02] shadow-lg";

  if (isMobile) {
    return (
      <div className="space-y-4">
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            原始文案
          </label>
          <Textarea
            placeholder="请输入您要优化的原始文案内容..."
            value={originalText}
            onChange={(e) => onOriginalTextChange(e.target.value)}
            className="min-h-[120px] resize-none border-gray-200/70 focus:border-blue-400 focus:ring-blue-400 rounded-xl bg-white/80 backdrop-blur-sm transition-all duration-200 text-sm"
          />
        </div>

        <ContentTypeSelector
          selectedTypes={contentTypes}
          onTypeChange={onContentTypesChange}
        />

        <div className="grid grid-cols-1 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              使用场景
            </label>
            <Select value={platform} onValueChange={onPlatformChange}>
              <SelectTrigger className="border-gray-200/70 focus:border-blue-400 focus:ring-blue-400 rounded-xl bg-white/80 backdrop-blur-sm h-11">
                <SelectValue placeholder="选择发布平台" />
              </SelectTrigger>
              <SelectContent className="bg-white/95 backdrop-blur-lg border-gray-200/70 rounded-xl shadow-xl">
                <SelectItem value="friendscircle" className="rounded-lg">朋友圈</SelectItem>
                <SelectItem value="xiaohongshu" className="rounded-lg">小红书</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              文案风格
            </label>
            <Select value={style || "xiaohongshu"} onValueChange={onStyleChange}>
              <SelectTrigger className="border-gray-200/70 focus:border-blue-400 focus:ring-blue-400 rounded-xl bg-white/80 backdrop-blur-sm h-11">
                <SelectValue placeholder="选择文案风格" />
              </SelectTrigger>
              <SelectContent className="bg-white/95 backdrop-blur-lg border-gray-200/70 rounded-xl shadow-xl">
                <SelectItem value="xiaohongshu" className="rounded-lg">小红书风格</SelectItem>
                <SelectItem value="authentic" className="rounded-lg">活人感</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>

        <Button 
          onClick={handleOptimize}
          disabled={isOptimizing || (contentTypes.length === 0 && !originalText.trim())}
          className={buttonClassName}
        >
          <ButtonIcon className="w-4 h-4 mr-2" />
          {buttonText}
        </Button>
      </div>
    );
  }

  // PC端保持原有设计
  return (
    <div className="space-y-8">
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-3">
          原始文案
        </label>
        <Textarea
          placeholder="请输入您要优化的原始文案内容..."
          value={originalText}
          onChange={(e) => onOriginalTextChange(e.target.value)}
          className="min-h-[220px] resize-none border-gray-200/70 focus:border-blue-400 focus:ring-blue-400 rounded-2xl bg-white/80 backdrop-blur-sm transition-all duration-200"
        />
      </div>

      <ContentTypeSelector
        selectedTypes={contentTypes}
        onTypeChange={onContentTypesChange}
      />

      <div className="grid grid-cols-2 gap-6">
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-3">
            使用场景
          </label>
          <Select value={platform} onValueChange={onPlatformChange}>
            <SelectTrigger className="border-gray-200/70 focus:border-blue-400 focus:ring-blue-400 rounded-2xl bg-white/80 backdrop-blur-sm h-12">
              <SelectValue placeholder="选择发布平台" />
            </SelectTrigger>
            <SelectContent className="bg-white/95 backdrop-blur-lg border-gray-200/70 rounded-2xl shadow-xl">
              <SelectItem value="friendscircle" className="rounded-xl">朋友圈</SelectItem>
              <SelectItem value="xiaohongshu" className="rounded-xl">小红书</SelectItem>
            </SelectContent>
          </Select>
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-3">
            文案风格
          </label>
          <Select value={style || "xiaohongshu"} onValueChange={onStyleChange}>
            <SelectTrigger className="border-gray-200/70 focus:border-blue-400 focus:ring-blue-400 rounded-2xl bg-white/80 backdrop-blur-sm h-12">
              <SelectValue placeholder="选择文案风格" />
            </SelectTrigger>
            <SelectContent className="bg-white/95 backdrop-blur-lg border-gray-200/70 rounded-2xl shadow-xl">
              <SelectItem value="xiaohongshu" className="rounded-xl">小红书风格</SelectItem>
              <SelectItem value="authentic" className="rounded-xl">活人感</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>

      <Button 
        onClick={handleOptimize}
        disabled={isOptimizing || (contentTypes.length === 0 && !originalText.trim())}
        className={isRegenerate 
          ? "w-full bg-gradient-to-r from-green-500 to-emerald-600 hover:from-green-600 hover:to-emerald-700 text-white py-4 rounded-2xl font-medium text-lg transition-all duration-200 transform hover:scale-[1.02] shadow-lg"
          : "w-full bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 text-white py-4 rounded-2xl font-medium text-lg transition-all duration-200 transform hover:scale-[1.02] shadow-lg"
        }
      >
        <ButtonIcon className="w-5 h-5 mr-3" />
        {buttonText}
      </Button>
    </div>
  );
};

export default OptimizationInput;
