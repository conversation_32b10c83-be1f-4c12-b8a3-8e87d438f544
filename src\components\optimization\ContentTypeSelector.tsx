
import React from 'react';
import { FileText, Video } from 'lucide-react';

interface ContentTypeSelectorProps {
  selectedTypes: string[];
  onTypeChange: (types: string[]) => void;
}

const contentTypeOptions = [
  {
    id: 'copywriting',
    label: '文案',
    icon: FileText,
    description: '优化文案内容'
  },
  {
    id: 'video-script',
    label: '短视频脚本',
    icon: Video,
    description: '生成短视频脚本'
  }
];

const ContentTypeSelector: React.FC<ContentTypeSelectorProps> = ({
  selectedTypes,
  onTypeChange
}) => {
  const toggleType = (typeId: string) => {
    if (selectedTypes.includes(typeId)) {
      onTypeChange(selectedTypes.filter(id => id !== typeId));
    } else {
      onTypeChange([...selectedTypes, typeId]);
    }
  };

  return (
    <div>
      <label className="block text-sm font-medium text-gray-700 mb-3">
        生成内容类型
      </label>
      <div className="flex flex-wrap gap-3">
        {contentTypeOptions.map((option) => {
          const Icon = option.icon;
          const isSelected = selectedTypes.includes(option.id);
          
          return (
            <button
              key={option.id}
              onClick={() => toggleType(option.id)}
              className={`
                relative flex items-center space-x-2 px-4 py-3 rounded-xl border-2 transition-all duration-200 overflow-hidden
                ${isSelected 
                  ? 'border-blue-500 text-blue-700 shadow-md' 
                  : 'border-gray-200 hover:border-gray-300 text-gray-600 hover:bg-gray-50'
                }
              `}
            >
              {/* Background layer for selected state */}
              {isSelected && (
                <div className="absolute inset-0 bg-blue-50 rounded-[10px]" />
              )}
              
              {/* Content layer */}
              <div className="relative z-10 flex items-center space-x-2">
                <Icon className="w-4 h-4" />
                <span className="font-medium">{option.label}</span>
              </div>
            </button>
          );
        })}
      </div>
      {selectedTypes.length === 0 && (
        <p className="text-xs text-gray-500 mt-2">请至少选择一种内容类型</p>
      )}
    </div>
  );
};

export default ContentTypeSelector;
