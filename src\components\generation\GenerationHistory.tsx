
import React from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Copy, Trash2, Calendar, FileText } from 'lucide-react';
import { formatDistanceToNow } from 'date-fns';
import { zhCN } from 'date-fns/locale';
import { GenerationHistoryItem } from '@/hooks/useGenerationHistory';

interface GenerationHistoryProps {
  historyItems: GenerationHistoryItem[];
  isLoading: boolean;
  onCopyContent: (content: string) => void;
  onDeleteItem: (id: string) => void;
  onRefresh: () => void;
}

const GenerationHistory: React.FC<GenerationHistoryProps> = ({
  historyItems,
  isLoading,
  onCopyContent,
  onDeleteItem,
  onRefresh
}) => {
  const domainMap: Record<string, any> = {
    marriage: { title: "婚姻家庭", icon: "💝" },
    labor: { title: "劳动用工", icon: "💼" },
    debt: { title: "债务纠纷", icon: "💳" },
    property: { title: "房产纠纷", icon: "🏠" },
    corporate: { title: "公司法务", icon: "🏢" },
    consumer: { title: "消费维权", icon: "🛡️" },
    criminal: { title: "刑事辩护", icon: "⚖️" },
    contract: { title: "合同审查", icon: "📄" }
  };

  if (isLoading) {
    return (
      <div className="space-y-4">
        {[...Array(3)].map((_, index) => (
          <Card key={index} className="animate-pulse rounded-2xl border-0 bg-gradient-to-br from-gray-50 to-gray-100 shadow-lg">
            <CardContent className="p-6">
              <div className="space-y-3">
                <div className="h-4 bg-gray-200 rounded-xl w-3/4"></div>
                <div className="h-3 bg-gray-200 rounded-xl w-1/2"></div>
                <div className="h-20 bg-gray-200 rounded-xl"></div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    );
  }

  if (historyItems.length === 0) {
    return (
      <Card className="rounded-2xl border-0 bg-gradient-to-br from-blue-50/50 to-indigo-50/50 shadow-lg backdrop-blur-sm">
        <CardContent className="p-8 text-center">
          <div className="w-16 h-16 mx-auto mb-4 bg-gradient-to-br from-blue-100 to-indigo-100 rounded-2xl flex items-center justify-center">
            <FileText className="w-8 h-8 text-blue-600" />
          </div>
          <h3 className="text-lg font-semibold text-gray-800 mb-2">暂无生成历史记录</h3>
          <p className="text-gray-600 mb-6">开始使用内容生成功能，您的历史记录将显示在这里</p>
          <Button 
            onClick={onRefresh} 
            variant="outline"
            className="rounded-xl px-6 py-3 bg-white/80 hover:bg-white border-gray-200 hover:border-blue-300 transition-all duration-300"
          >
            刷新记录
          </Button>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-4">
      {historyItems.map((item) => {
        const domainInfo = domainMap[item.domain] || { title: item.domain, icon: "📝" };
        const timeAgo = formatDistanceToNow(new Date(item.created_at), {
          addSuffix: true,
          locale: zhCN
        });

        return (
          <Card key={item.id} className="rounded-2xl border-0 bg-gradient-to-br from-white/90 to-gray-50/90 shadow-lg hover:shadow-xl transition-all duration-300 backdrop-blur-sm hover:scale-[1.02]">
            <CardHeader className="pb-3 px-6 pt-6">
              <div className="flex items-start justify-between">
                <div className="flex items-center space-x-3">
                  <div className="w-12 h-12 bg-gradient-to-br from-blue-100 to-indigo-100 rounded-2xl flex items-center justify-center">
                    <span className="text-xl">{domainInfo.icon}</span>
                  </div>
                  <div>
                    <CardTitle className="text-lg font-semibold text-gray-900 mb-1">
                      {domainInfo.title}
                    </CardTitle>
                    {item.topic && (
                      <div className="bg-gradient-to-r from-blue-500 to-indigo-500 text-white px-3 py-1 rounded-full text-xs font-medium">
                        主题: {item.topic}
                      </div>
                    )}
                  </div>
                </div>
                <div className="flex items-center space-x-2">
                  <Button
                    onClick={() => onCopyContent(item.generated_content)}
                    size="sm"
                    variant="outline"
                    className="h-9 w-9 p-0 rounded-xl bg-white/80 hover:bg-blue-50 border-gray-200 hover:border-blue-300 transition-all duration-200"
                  >
                    <Copy className="w-4 h-4 text-blue-600" />
                  </Button>
                  <Button
                    onClick={() => onDeleteItem(item.id)}
                    size="sm"
                    variant="outline"
                    className="h-9 w-9 p-0 rounded-xl bg-white/80 hover:bg-red-50 border-gray-200 hover:border-red-300 transition-all duration-200"
                  >
                    <Trash2 className="w-4 h-4 text-red-600" />
                  </Button>
                </div>
              </div>
            </CardHeader>
            <CardContent className="pt-0 px-6 pb-6">
              <div className="bg-gradient-to-br from-gray-50/80 to-blue-50/30 rounded-2xl p-4 mb-4 border border-gray-100/50">
                <p className="text-sm text-gray-700 line-clamp-3 leading-relaxed">
                  {item.generated_content}
                </p>
              </div>
              <div className="flex items-center justify-between text-xs">
                <div className="flex items-center space-x-2 text-gray-500">
                  <div className="w-5 h-5 bg-gradient-to-br from-gray-100 to-gray-200 rounded-lg flex items-center justify-center">
                    <Calendar className="w-3 h-3" />
                  </div>
                  <span className="font-medium">{timeAgo}</span>
                </div>
                <div className="bg-gradient-to-r from-purple-100 to-blue-100 text-purple-700 px-3 py-1 rounded-full text-xs font-medium">
                  {item.generated_content.length} 字符
                </div>
              </div>
            </CardContent>
          </Card>
        );
      })}
    </div>
  );
};

export default GenerationHistory;
