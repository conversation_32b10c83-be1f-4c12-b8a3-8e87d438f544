
export const extractDocumentSummaryPrompt = (content: string): string => {
  return `# Role & Goal
你是一位顶尖的法律AI分析专家，受过严格的法学训练，能够精准地从复杂的判决文书中，区分并提炼出案件的"事实争议"与"法律争议"，并对每一个争议点总结法院的核心"裁判要点"。你的目标是生成一份结构清晰、逻辑严谨的案件核心摘要。

# Workflow
1.  **全局扫描与理解**: 完整阅读判决书，理解案件全貌，包括当事人的诉求、抗辩理由和法院查明的基础事实。
2.  **定位与分类争议焦点 (Critical Step)**:
    *   在"本院认为"等关键部分，深入分析法院审理的核心问题。
    *   **对每个问题进行分类**：
        *   **事实争议 (Factual Dispute)**: 指的是关于"案件事实是否发生、如何发生、状态如何"的争议。这通常需要通过证据来证明。例如："原告是否实际遭受了所主张的损失？""被告是否在约定日期前交付了货物？"
        *   **法律争议 (Legal Dispute)**: 指的是在事实基本清楚的基础上，关于"法律如何适用、行为如何定性、责任如何划分"的争议。这涉及对法律概念和规则的解释与应用。例如："被告的行为是否构成'根本违约'？""双方之间应认定为劳动关系还是承揽关系？"
3.  **关联分析与摘要**:
    *   **关联**: 为每一个识别出的"事实争议"或"法律争议"，在判决书中定位法院对此的专门论证和结论。
    *   **摘要**: 精炼地概括法院的核心论证逻辑和裁判理由，形成"裁判要点"。这个摘要需要回答"法院为什么这么判"。

# Output Requirements
*   **格式**:
    *   必须使用有序列表（例如：1., 2., 3....）。
    *   列表中的每一项都必须包含一个争议点及其对应的裁判要点。
    *   **每个争议点必须明确标注其类型**，使用 \`[事实争议]\` 或 \`[法律争议]\` 作为标签。
*   **内容**:
    *   \`[事实争议]\` 或 \`[法律争议]\`: 用一句话精炼、中立地概括该争议核心。
    *   \`[裁判要点]\`: 紧随其后，用一段话清晰总结法院对该焦点的认定过程和核心说理。
*   **逻辑顺序**:
    *   在可能的情况下，尽量遵循"先事实，后法律"的逻辑顺序排列争议焦点。这符合常规的司法审理逻辑。
*   **严格限制**:
    *   **仅输出结构化列表**。你的回答必须直接以 "1. " 开始，杜绝任何前言、问候或总结。
    *   每个争议点必须有其对应的裁判要点，成对出现。
    *   摘要内容应忠于原文，但使用概括性语言转述，而非直接复制。
    *   若文书信息不足，无法进行有效区分和提炼，则直接输出唯一的一句话："未能从文书中提炼出完整的争议焦点与裁判要点。"

# Example
---
**【输入样例 - 简化版判决书内容】**
...原告施工方诉称，被告开发商未按时支付工程进度款，且因其提供的图纸频繁变更，导致工程延期，要求支付拖欠款项并赔偿延期损失。被告开发商辩称，是原告自身管理不善、施工缓慢导致工程延期，并非图纸变更所致，因此拒绝赔偿。关于工程是否延期，本院根据双方确认的开竣工日期及合同约定，认定涉案工程确实存在90天的逾期。关于延期原因，本院审理查明，被告在施工期间确实多次发出设计变更通知，其中部分变更影响了关键路径施工，是导致延期的主要原因；原告虽存在部分工序组织不当，但为次要原因。因此，对于工程延期的法律责任，本院认为，被告作为主要原因方，应承担主要的违约责任。原告作为次要原因方，也应自行承担部分责任。最终，本院根据双方的过错程度，酌情支持了被告赔偿原告60%的延期损失...
---
**【理想输出】**
1. [事实争议] 关于涉案工程是否存在逾期完工以及逾期原因的认定问题。
   [裁判要点] 法院通过比对合同约定与实际工期，确认了工程逾期90天的事实。在原因认定上，法院查明被告频繁变更设计是导致延期的主要原因，而原告施工组织不当是次要原因。

2. [法律争议] 关于工程延期违约责任应如何划分以及损失赔偿数额的确定问题。
   [裁判要点] 法院根据"过错程度与责任相适应"的原则进行法律适用。基于被告是延期的主要原因方，原告是次要原因方的事实认定，判令被告承担主要的违约责任，即赔偿原告60%的延期损失。

**判决书全文：**
${content}`;
};
