
import React from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Copy, Trash2, Calendar, FileText, Sparkles, Video } from 'lucide-react';
import { formatDistanceToNow } from 'date-fns';
import { zhCN } from 'date-fns/locale';
import { OptimizationHistory as OptimizationHistoryItem } from '@/hooks/useHistory';

interface OptimizationHistoryProps {
  historyItems: OptimizationHistoryItem[];
  isLoading: boolean;
  onCopyContent: (content: string, type: string) => void;
  onDeleteItem: (item: OptimizationHistoryItem) => void;
  onRefresh: () => void;
}

const OptimizationHistory: React.FC<OptimizationHistoryProps> = ({
  historyItems,
  isLoading,
  onCopyContent,
  onDeleteItem,
  onRefresh
}) => {
  if (isLoading) {
    return (
      <div className="space-y-4">
        {[...Array(3)].map((_, index) => (
          <Card key={index} className="animate-pulse rounded-2xl border-0 bg-gradient-to-br from-gray-50 to-gray-100 shadow-lg">
            <CardContent className="p-6">
              <div className="space-y-3">
                <div className="h-4 bg-gray-200 rounded-xl w-3/4"></div>
                <div className="h-3 bg-gray-200 rounded-xl w-1/2"></div>
                <div className="h-20 bg-gray-200 rounded-xl"></div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    );
  }

  if (historyItems.length === 0) {
    return (
      <Card className="rounded-2xl border-0 bg-gradient-to-br from-blue-50/50 to-indigo-50/50 shadow-lg backdrop-blur-sm">
        <CardContent className="p-8 text-center">
          <div className="w-16 h-16 mx-auto mb-4 bg-gradient-to-br from-blue-100 to-indigo-100 rounded-2xl flex items-center justify-center">
            <Sparkles className="w-8 h-8 text-blue-600" />
          </div>
          <h3 className="text-lg font-semibold text-gray-800 mb-2">暂无优化历史记录</h3>
          <p className="text-gray-600 mb-6">开始使用文案优化功能，您的历史记录将显示在这里</p>
          <Button 
            onClick={onRefresh} 
            variant="outline"
            className="rounded-xl px-6 py-3 bg-white/80 hover:bg-white border-gray-200 hover:border-blue-300 transition-all duration-300"
          >
            刷新记录
          </Button>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-4">
      {historyItems.map((item) => {
        const timeAgo = formatDistanceToNow(new Date(item.created_at), {
          addSuffix: true,
          locale: zhCN
        });

        const isVideoScript = item.content_type === 'video-script';
        const contentTypeLabel = isVideoScript ? '短视频脚本' : '文案优化';
        const ContentIcon = isVideoScript ? Video : Sparkles;
        const iconColor = isVideoScript ? 'text-green-600' : 'text-purple-600';
        const bgColor = isVideoScript ? 'from-green-100 to-emerald-100' : 'from-purple-100 to-pink-100';

        return (
          <Card key={item.id} className="rounded-2xl border-0 bg-gradient-to-br from-white/90 to-gray-50/90 shadow-lg hover:shadow-xl transition-all duration-300 backdrop-blur-sm hover:scale-[1.02]">
            <CardHeader className="pb-3 px-6 pt-6">
              <div className="flex items-start justify-between">
                <div className="flex items-center space-x-3">
                  <div className={`w-12 h-12 bg-gradient-to-br ${bgColor} rounded-2xl flex items-center justify-center`}>
                    <ContentIcon className={`w-6 h-6 ${iconColor}`} />
                  </div>
                  <div>
                    <CardTitle className="text-lg font-semibold text-gray-900 mb-1">
                      {contentTypeLabel}
                    </CardTitle>
                    <div className="flex items-center space-x-2">
                      {item.platform && (
                        <div className="bg-gradient-to-r from-blue-500 to-indigo-500 text-white px-3 py-1 rounded-full text-xs font-medium">
                          平台: {item.platform}
                        </div>
                      )}
                      {item.style && (
                        <div className="bg-gradient-to-r from-green-500 to-emerald-500 text-white px-3 py-1 rounded-full text-xs font-medium">
                          风格: {item.style}
                        </div>
                      )}
                    </div>
                  </div>
                </div>
                <div className="flex items-center space-x-2">
                  <Button
                    onClick={() => onCopyContent(item.optimized_text, contentTypeLabel)}
                    size="sm"
                    variant="outline"
                    className="h-9 w-9 p-0 rounded-xl bg-white/80 hover:bg-blue-50 border-gray-200 hover:border-blue-300 transition-all duration-200"
                  >
                    <Copy className="w-4 h-4 text-blue-600" />
                  </Button>
                  <Button
                    onClick={() => onDeleteItem(item)}
                    size="sm"
                    variant="outline"
                    className="h-9 w-9 p-0 rounded-xl bg-white/80 hover:bg-red-50 border-gray-200 hover:border-red-300 transition-all duration-200"
                  >
                    <Trash2 className="w-4 h-4 text-red-600" />
                  </Button>
                </div>
              </div>
            </CardHeader>
            <CardContent className="pt-0 px-6 pb-6">
              {/* 原始文案 */}
              <div className="bg-gradient-to-br from-gray-50/80 to-blue-50/30 rounded-2xl p-4 mb-3 border border-gray-100/50">
                <div className="flex items-center space-x-2 mb-2">
                  <FileText className="w-4 h-4 text-gray-600" />
                  <span className="text-sm font-medium text-gray-800">原始文案</span>
                </div>
                <p className="text-sm text-gray-700 line-clamp-2 leading-relaxed">
                  {item.original_text}
                </p>
              </div>
              
              {/* 优化后文案 */}
              <div className={`bg-gradient-to-br ${isVideoScript ? 'from-green-50/80 to-emerald-50/30 border-green-100/50' : 'from-purple-50/80 to-pink-50/30 border-purple-100/50'} rounded-2xl p-4 mb-4 border`}>
                <div className="flex items-center space-x-2 mb-2">
                  <ContentIcon className={`w-4 h-4 ${iconColor}`} />
                  <span className={`text-sm font-medium ${isVideoScript ? 'text-green-800' : 'text-purple-800'}`}>
                    {isVideoScript ? '短视频脚本' : '优化后文案'}
                  </span>
                </div>
                <p className="text-sm text-gray-700 line-clamp-3 leading-relaxed">
                  {item.optimized_text}
                </p>
              </div>
              
              <div className="flex items-center justify-between text-xs">
                <div className="flex items-center space-x-2 text-gray-500">
                  <div className="w-5 h-5 bg-gradient-to-br from-gray-100 to-gray-200 rounded-lg flex items-center justify-center">
                    <Calendar className="w-3 h-3" />
                  </div>
                  <span className="font-medium">{timeAgo}</span>
                </div>
                <div className={`bg-gradient-to-r ${isVideoScript ? 'from-green-100 to-emerald-100 text-green-700' : 'from-purple-100 to-blue-100 text-purple-700'} px-3 py-1 rounded-full text-xs font-medium`}>
                  {item.optimized_text.length} 字符
                </div>
              </div>
            </CardContent>
          </Card>
        );
      })}
    </div>
  );
};

export default OptimizationHistory;
