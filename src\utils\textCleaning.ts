
// 改进的文本清理函数 - 保持段落结构
export const cleanDisplayText = (text: string): string => {
  return text
    // 移除markdown格式符号，但保留内容
    .replace(/\*{1,3}([^*]*)\*{1,3}/g, '$1')
    .replace(/_{1,3}([^_]*)_{1,3}/g, '$1')
    .replace(/#{1,6}\s*/g, '')
    .replace(/`{1,3}([^`]*)`{1,3}/g, '$1')
    .replace(/\[([^\]]+)\]\([^)]+\)/g, '$1')
    // 移除特殊符号
    .replace(/[【】\[\]]/g, '')
    .replace(/[《》<>]/g, '')
    .replace(/^\s*[-*+]\s*/gm, '')
    .replace(/^\s*\d+\.\s*/gm, '')
    .replace(/^\s*>\s*/gm, '')
    // 移除装饰线和多余符号
    .replace(/[-=]{3,}/g, '')
    .replace(/\*{2,}/g, '')
    .replace(/#{2,}/g, '')
    // 保持段落结构：保留双换行作为段落分隔
    .replace(/\n{4,}/g, '\n\n\n')  // 最多3个换行
    .replace(/\n{2}/g, '||PARAGRAPH||')  // 临时标记段落分隔
    // 清理单行内的多余空格
    .replace(/[ \t]{2,}/g, ' ')
    // 恢复段落分隔
    .replace(/\|\|PARAGRAPH\|\|/g, '\n\n')
    // 清理首尾空白
    .trim()
}
