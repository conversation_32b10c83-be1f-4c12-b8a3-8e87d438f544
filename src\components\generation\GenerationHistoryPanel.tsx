
import React, { useState, useEffect, useCallback } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, Sheet<PERSON><PERSON><PERSON>, SheetOverlay, SheetPortal } from '@/components/ui/sheet';
import { Button } from '@/components/ui/button';
import { ScrollArea } from '@/components/ui/scroll-area';
import { History } from 'lucide-react';
import { useGenerationHistory } from '@/hooks/useGenerationHistory';
import GenerationHistory from './GenerationHistory';
import * as SheetPrimitive from "@radix-ui/react-dialog"

interface GenerationHistoryPanelProps {
  domain?: string;
  trigger?: React.ReactNode;
}

const GenerationHistoryPanel: React.FC<GenerationHistoryPanelProps> = ({ domain, trigger }) => {
  const [isOpen, setIsOpen] = useState(false);
  const {
    generationHistory,
    isLoadingHistory,
    fetchGenerationHistory,
    deleteHistoryItem,
    copyContent,
  } = useGenerationHistory();

  // 只在面板打开时获取历史记录，避免重复请求
  const handleOpenChange = useCallback((open: boolean) => {
    setIsOpen(open);
    if (open && generationHistory.length === 0 && !isLoadingHistory) {
      fetchGenerationHistory();
    }
  }, [fetchGenerationHistory, generationHistory.length, isLoadingHistory]);

  // 如果指定了domain，过滤历史记录
  const filteredHistory = domain 
    ? generationHistory.filter(item => item.domain === domain)
    : generationHistory;

  const defaultTrigger = (
    <Button
      variant="ghost"
      size="sm"
      className="flex items-center space-x-2 rounded-xl bg-white/80 hover:bg-gray-50/80 transition-all duration-200"
    >
      <History className="w-4 h-4" />
      <span>历史记录</span>
    </Button>
  );

  return (
    <SheetPrimitive.Root open={isOpen} onOpenChange={handleOpenChange}>
      <SheetTrigger asChild>
        {trigger || defaultTrigger}
      </SheetTrigger>
      <SheetPortal>
        <SheetPrimitive.Overlay className="fixed inset-0 z-50 bg-black/20 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0" />
        <SheetPrimitive.Content className="fixed z-50 gap-4 bg-background p-6 shadow-lg transition ease-in-out data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:duration-300 data-[state=open]:duration-500 inset-y-0 right-0 h-full w-full data-[state=closed]:slide-out-to-right data-[state=open]:slide-in-from-right sm:max-w-2xl bg-gradient-to-br from-blue-50/70 via-white to-indigo-50/70 flex flex-col rounded-l-3xl">
          <SheetHeader className="pb-6 flex-shrink-0">
            <div className="flex items-center justify-between">
              <SheetTitle className="text-xl font-semibold text-gray-900 flex items-center space-x-2">
                <div className="w-8 h-8 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-lg flex items-center justify-center">
                  <History className="w-4 h-4 text-white" />
                </div>
                <span>{domain ? `${domain} 领域` : '全部'}历史记录</span>
              </SheetTitle>
            </div>
            {domain && (
              <p className="text-sm text-gray-600 mt-2">
                显示 {domain} 领域的生成历史记录
              </p>
            )}
          </SheetHeader>

          <ScrollArea className="flex-1 pr-2">
            <GenerationHistory
              historyItems={filteredHistory}
              isLoading={isLoadingHistory}
              onCopyContent={copyContent}
              onDeleteItem={deleteHistoryItem}
              onRefresh={fetchGenerationHistory}
            />
          </ScrollArea>
          
          <SheetPrimitive.Close className="absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-secondary">
            <span className="sr-only">Close</span>
          </SheetPrimitive.Close>
        </SheetPrimitive.Content>
      </SheetPortal>
    </SheetPrimitive.Root>
  );
};

export default GenerationHistoryPanel;
