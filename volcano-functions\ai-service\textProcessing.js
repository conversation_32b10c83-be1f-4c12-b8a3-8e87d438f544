// 从现有代码移植的文本处理功能

function parseTopics(text) {
  if (!text || typeof text !== 'string') {
    console.log('Invalid text input for parseTopics:', text);
    return [];
  }

  console.log('Parsing topics from text:', text.substring(0, 200));
  
  // 尝试多种解析模式
  const patterns = [
    // 1. 带序号的格式：1. 主题内容
    /^\d+\.\s*(.+)$/gm,
    // 2. 带中文序号的格式：一、主题内容
    /^[一二三四五六七八九十]+[、.]\s*(.+)$/gm,
    // 3. 带破折号的格式：- 主题内容
    /^[-•]\s*(.+)$/gm,
    // 4. 纯粹的行分割（每行一个主题）
    /^(.+)$/gm
  ];

  let topics = [];
  
  for (const pattern of patterns) {
    const matches = Array.from(text.matchAll(pattern));
    if (matches.length > 0) {
      topics = matches
        .map(match => match[1] ? match[1].trim() : match[0].trim())
        .filter(topic => {
          // 过滤掉太短或包含无关内容的行
          return topic.length > 3 && 
                 topic.length < 100 &&
                 !topic.includes('以下是') &&
                 !topic.includes('选题推荐') &&
                 !topic.includes('建议') &&
                 !topic.match(/^[【】\[\]（）()]+$/) &&
                 !topic.includes('注意：') &&
                 !topic.includes('提示：');
        })
        .slice(0, 10); // 最多返回10个主题
      
      if (topics.length >= 3) {
        console.log(`Found ${topics.length} topics using pattern`);
        break;
      }
    }
  }

  // 最终清理和去重
  topics = topics
    .map(topic => {
      // 移除序号和特殊字符
      return topic
        .replace(/^[\d\s\.、]+/, '') // 移除开头的数字和序号
        .replace(/^[一二三四五六七八九十]+[、.]/, '') // 移除中文序号
        .replace(/^[-•]\s*/, '') // 移除破折号
        .replace(/[【】\[\]（）()]/g, '') // 移除括号
        .trim();
    })
    .filter((topic, index, arr) => {
      // 去重和最终过滤
      return topic.length > 0 && 
             topic.length < 80 &&
             arr.indexOf(topic) === index;
    });

  console.log('Final parsed topics:', topics);
  return topics;
}

function parseGeneratedContent(text) {
  if (!text || typeof text !== 'string') {
    console.log('Invalid text input for parseGeneratedContent:', text);
    return [];
  }

  console.log('Parsing generated content from text:', text.substring(0, 200));
  
  // 尝试按不同分隔符分割内容
  const separators = [
    /---+/g,           // 三个或更多破折号
    /===+/g,           // 三个或更多等号
    /\n\s*\n\s*\n/g,   // 两个或更多空行
    /\d+\.\s*\n/g,     // 序号后面的换行
  ];

  let contents = [];
  
  for (const separator of separators) {
    const parts = text.split(separator);
    if (parts.length > 1) {
      contents = parts
        .map(part => part.trim())
        .filter(part => part.length > 50) // 过滤太短的内容
        .slice(0, 5); // 最多返回5个内容
      
      if (contents.length > 0) {
        console.log(`Found ${contents.length} contents using separator`);
        break;
      }
    }
  }

  // 如果分割失败，返回整个文本作为单个内容
  if (contents.length === 0 && text.length > 20) {
    contents = [text.trim()];
  }

  console.log('Final parsed contents count:', contents.length);
  return contents;
}

function cleanXiaohongshuContent(text) {
  if (!text || typeof text !== 'string') {
    return '';
  }
  
  console.log('Cleaning xiaohongshu content, original length:', text.length);
  
  let cleaned = text
    // 移除常见的AI生成标识
    .replace(/^(以下是|这是|生成的|AI生成|小红书文案|文案如下)[：:]\s*/i, '')
    .replace(/^(当然|好的|没问题)[，,!！]*\s*/i, '')
    
    // 移除引用标记和解释性文字
    .replace(/^【[^】]*】\s*/gm, '')
    .replace(/^「[^」]*」\s*/gm, '')
    .replace(/\(注[：:][^)]*\)/g, '')
    .replace(/（注[：:][^）]*）/g, '')
    
    // 清理多余的换行和空格
    .replace(/\n{3,}/g, '\n\n')
    .replace(/[ \t]+/g, ' ')
    .trim();
  
  // 确保表情符号和话题标签的格式正确
  cleaned = cleaned
    .replace(/\s*#\s*/g, ' #')
    .replace(/\s*@\s*/g, ' @')
    // .replace(/([😀-🿿])\s+/g, '$1 ')
    .trim();
  
  console.log('Cleaned xiaohongshu content, final length:', cleaned.length);
  return cleaned;
}

function cleanAIGeneratedContent(text) {
  if (!text || typeof text !== 'string') {
    return '';
  }
  
  console.log('Cleaning AI generated content, original length:', text.length);
  
  let cleaned = text
    // 移除AI生成的标识和解释
    .replace(/^(我来为您|让我来|以下是|这里是)[^。]*。?\s*/i, '')
    .replace(/^(根据您的要求|按照您的需求)[^。]*。?\s*/i, '')
    .replace(/^(好的|没问题|当然可以)[，,!！]*\s*/i, '')
    
    // 移除括号内的解释性文字（保留必要的括号内容）
    .replace(/\([^)]*注[：:][^)]*\)/g, '')
    .replace(/（[^）]*注[：:][^）]*）/g, '')
    .replace(/\[[^\]]*注[：:][^\]]*\]/g, '')
    .replace(/【[^】]*注[：:][^】]*】/g, '')
    
    // 清理格式标记
    .replace(/^\*\*([^*]+)\*\*$/gm, '$1') // 移除整行的粗体标记，保留内容
    .replace(/^#+\s*/gm, '') // 移除markdown标题标记
    
    // 清理多余的空白字符
    .replace(/\n{3,}/g, '\n\n')
    .replace(/[ \t]+/g, ' ')
    .replace(/^\s+|\s+$/g, ''); // trim
  
  console.log('Cleaned AI content, final length:', cleaned.length);
  return cleaned;
}

module.exports = {
  parseTopics,
  parseGeneratedContent,
  cleanXiaohongshuContent,
  cleanAIGeneratedContent
};