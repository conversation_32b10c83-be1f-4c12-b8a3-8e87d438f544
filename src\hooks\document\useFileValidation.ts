
import { useToast } from '@/hooks/use-toast';
import { validateFile, getErrorMessage } from '@/utils/fileValidation';

export const useFileValidation = () => {
  const { toast } = useToast();

  const validateSelectedFile = (file: File): boolean => {
    const validation = validateFile(file);
    
    if (!validation.isValid) {
      toast({
        title: validation.error?.includes('格式') ? "文件格式不支持" : "文件过大",
        description: validation.error,
        variant: "destructive"
      });
      return false;
    }
    
    return true;
  };

  const showErrorToast = (error: Error) => {
    const { title, message } = getErrorMessage(error);
    toast({
      title,
      description: message,
      variant: "destructive"
    });
  };

  const showSuccessToast = (title: string, description: string) => {
    toast({
      title,
      description
    });
  };

  return {
    validateSelectedFile,
    showErrorToast,
    showSuccessToast
  };
};
