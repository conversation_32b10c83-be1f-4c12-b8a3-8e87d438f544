
import React from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { FileText, ArrowRight } from 'lucide-react';
import GenerationHistoryPanel from './GenerationHistoryPanel';

interface GenerationTabProps {
  onGenerateContent: () => void;
}

const GenerationTab: React.FC<GenerationTabProps> = ({
  onGenerateContent
}) => {
  return (
    <Card className="max-w-3xl mx-auto shadow-2xl border-0 bg-white/90 backdrop-blur-lg rounded-3xl overflow-hidden relative">
      {/* 历史记录按钮 - 右上角 */}
      <div className="absolute top-6 right-6 z-10">
        <GenerationHistoryPanel />
      </div>

      <CardContent className="p-10 text-center">
        <div className="mb-10">
          <div className="flex items-center justify-center gap-4 mb-6">
            <div className="bg-gradient-to-br from-blue-500 to-indigo-600 w-16 h-16 rounded-2xl flex items-center justify-center shadow-lg">
              <FileText className="w-8 h-8 text-white" />
            </div>
            <h2 className="text-3xl font-bold text-gray-900">内容生成</h2>
          </div>
          <p className="text-gray-600 text-lg">选择您的专业领域，AI将为您生成专业的法律营销文案</p>
        </div>

        <div className="space-y-8">
          <div className="bg-gradient-to-br from-blue-50/80 to-indigo-50/50 rounded-3xl p-8 backdrop-blur-sm border border-blue-100/50">
            <h3 className="text-xl font-semibold text-gray-900 mb-6">功能特点</h3>
            <ul className="text-sm text-gray-600 space-y-4">
              <li className="flex items-center space-x-3">
                <div className="w-2 h-2 bg-blue-500 rounded-full flex-shrink-0"></div>
                <span>8个主要法律领域可选</span>
              </li>
              <li className="flex items-center space-x-3">
                <div className="w-2 h-2 bg-green-500 rounded-full flex-shrink-0"></div>
                <span>每个领域生成10条专业文案</span>
              </li>
              <li className="flex items-center space-x-3">
                <div className="w-2 h-2 bg-purple-500 rounded-full flex-shrink-0"></div>
                <span>内容符合法律营销规范</span>
              </li>
              <li className="flex items-center space-x-3">
                <div className="w-2 h-2 bg-orange-500 rounded-full flex-shrink-0"></div>
                <span>适合多平台发布使用</span>
              </li>
            </ul>
          </div>

          <Button onClick={onGenerateContent} className="w-full bg-gradient-to-r from-blue-500 to-indigo-600 hover:from-blue-600 hover:to-indigo-700 text-white py-5 rounded-2xl font-medium text-xl transition-all duration-200 transform hover:scale-[1.02] shadow-lg">
            选择法律领域
            <ArrowRight className="w-6 h-6 ml-3" />
          </Button>
        </div>
      </CardContent>
    </Card>
  );
};

export default GenerationTab;
