
import { Card, CardContent } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Copy, Hash } from "lucide-react";
import { toast } from "@/hooks/use-toast";

interface ContentCardProps {
  content: string;
  index: number;
  domainTag: string;
}

const ContentCard = ({ content, index, domainTag }: ContentCardProps) => {
  const copyContent = (content: string, index: number) => {
    navigator.clipboard.writeText(content);
    toast({
      title: "复制成功",
      description: `第${index + 1}条文案已复制到剪贴板`,
    });
  };

  // 处理内容显示，保持结构化分段
  const formatDisplayContent = (text: string) => {
    return text
      .split('\n')
      .map((paragraph, idx) => {
        if (paragraph.trim()) {
          return (
            <p key={idx} className="mb-2 last:mb-0 leading-relaxed text-sm">
              {paragraph.trim()}
            </p>
          );
        }
        return null;
      })
      .filter(Boolean);
  };

  return (
    <Card className="shadow-lg border-0 bg-white/80 backdrop-blur hover:shadow-xl transition-all duration-300 h-80 flex flex-col rounded-3xl">
      <CardContent className="p-6 flex flex-col h-full">
        <div className="flex justify-between items-start mb-4 flex-shrink-0">
          <div className="flex items-center space-x-2 flex-wrap">
            <div className="bg-gradient-to-br from-purple-400 to-purple-600 text-white px-3 py-1.5 rounded-full text-xs font-medium shadow-lg">
              #{index + 1}
            </div>
            <div className="bg-gradient-to-br from-blue-400 to-blue-600 text-white px-3 py-1.5 rounded-full text-xs font-medium shadow-lg flex items-center space-x-1">
              <Hash className="w-3 h-3" />
              <span>{domainTag}</span>
            </div>
            <div className="bg-gradient-to-br from-green-400 to-green-600 text-white px-3 py-1.5 rounded-full text-xs font-medium shadow-lg">
              #法律咨询
            </div>
          </div>
          <Button
            onClick={() => copyContent(content, index)}
            variant="outline"
            size="sm"
            className="flex items-center space-x-1.5 hover:bg-blue-50/80 hover:border-blue-300 text-sm px-4 py-2 h-auto flex-shrink-0 rounded-full backdrop-blur-sm border-gray-200/70 transition-all duration-200 shadow-sm hover:shadow-md"
          >
            <Copy className="w-3.5 h-3.5" />
            <span>复制</span>
          </Button>
        </div>
        
        <div className="text-gray-800 flex-1 overflow-y-auto scrollbar-thin scrollbar-thumb-gray-300 scrollbar-track-gray-100">
          {formatDisplayContent(content)}
        </div>
      </CardContent>
    </Card>
  );
};

export default ContentCard;
