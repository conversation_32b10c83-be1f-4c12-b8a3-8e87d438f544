import { Button } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Checkbox } from "@/components/ui/checkbox";
import { <PERSON>rkles, ArrowRight, CheckSquare, Square } from "lucide-react";
import { useNavigate } from "react-router-dom";
import { useState } from "react";

interface TopicsListProps {
  topics: string[];
  currentDomainInfo: {
    tag: string;
    title: string;
  };
  domain: string;
  onGenerateSelectedTopics: (selectedTopics: string[]) => void;
}

const TopicsList = ({ topics, currentDomainInfo, domain, onGenerateSelectedTopics }: TopicsListProps) => {
  const navigate = useNavigate();
  const [selectedTopics, setSelectedTopics] = useState<string[]>([]);

  const handleTopicCheck = (topic: string, checked: boolean) => {
    if (checked) {
      setSelectedTopics(prev => [...prev, topic]);
    } else {
      setSelectedTopics(prev => prev.filter(t => t !== topic));
    }
  };

  const handleCardClick = (topic: string) => {
    const isSelected = selectedTopics.includes(topic);
    handleTopicCheck(topic, !isSelected);
  };

  const handleGenerateSelected = () => {
    if (selectedTopics.length > 0) {
      onGenerateSelectedTopics(selectedTopics);
    }
  };

  // 一键全选/取消全选功能
  const handleToggleSelectAll = () => {
    if (selectedTopics.length === topics.length) {
      // 如果已全选，则取消全选
      setSelectedTopics([]);
    } else {
      // 否则全选
      setSelectedTopics([...topics]);
    }
  };

  const isAllSelected = selectedTopics.length === topics.length;

  // 格式化选题文本，将长文本分段显示
  const formatTopicText = (topic: string) => {
    // 按标点符号分段，但保持语义完整
    const segments = topic.split(/([。！？；])/);
    const formattedSegments: string[] = [];
    
    for (let i = 0; i < segments.length; i += 2) {
      const text = segments[i];
      const punctuation = segments[i + 1] || '';
      if (text && text.trim()) {
        formattedSegments.push(text.trim() + punctuation);
      }
    }
    
    return formattedSegments.length > 1 ? formattedSegments : [topic];
  };

  return (
    <div className="space-y-8 relative">
      <div className="text-center">
        <div className="bg-white/70 backdrop-blur-md rounded-3xl p-8 max-w-4xl mx-auto shadow-xl border border-white/50 mb-8">
          <h2 className="text-3xl font-bold text-gray-900 mb-4">
            🔥 {currentDomainInfo.title}热门选题
          </h2>
          <p className="text-lg text-gray-600 mb-6">
            以下是AI为您精选的10个爆款选题，勾选您需要的选题后点击生成文案
          </p>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {topics.map((topic, index) => {
          const formattedSegments = formatTopicText(topic);
          const isSelected = selectedTopics.includes(topic);
          
          return (
            <Card
              key={index}
              className={`hover:shadow-2xl transition-all duration-300 border-0 bg-white/80 backdrop-blur-md rounded-3xl overflow-hidden shadow-lg relative cursor-pointer ${
                isSelected ? 'ring-2 ring-blue-500 bg-blue-50/50' : ''
              }`}
              onClick={() => handleCardClick(topic)}
            >
              <CardContent className="p-8">
                <div className="flex items-start space-x-4">
                  <div className="bg-gradient-to-br from-blue-500 to-purple-600 text-white w-12 h-12 rounded-full flex items-center justify-center font-bold text-sm flex-shrink-0 shadow-lg">
                    {index + 1}
                  </div>
                  <div className="flex-1">
                    <div className="text-gray-800 font-medium leading-relaxed mb-4 space-y-2">
                      {formattedSegments.map((segment, segIndex) => (
                        <p key={segIndex} className="text-sm leading-relaxed">
                          {segment}
                        </p>
                      ))}
                    </div>
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-2 flex-wrap">
                        <span className="bg-gradient-to-br from-red-400 to-red-600 text-white px-3 py-1.5 rounded-full text-xs font-medium shadow-lg">
                          🔥 热门
                        </span>
                        <span className="bg-gradient-to-br from-blue-400 to-blue-600 text-white px-3 py-1.5 rounded-full text-xs font-medium shadow-lg">
                          #{currentDomainInfo.tag}
                        </span>
                      </div>
                      
                      {/* 现代化的选择按钮 */}
                      <div className="flex items-center space-x-2" onClick={(e) => e.stopPropagation()}>
                        <Checkbox
                          id={`topic-${index}`}
                          checked={selectedTopics.includes(topic)}
                          onCheckedChange={(checked) => handleTopicCheck(topic, checked as boolean)}
                          className="h-5 w-5 rounded-full border-2 border-gray-300 data-[state=checked]:bg-blue-500 data-[state=checked]:border-blue-500 data-[state=checked]:text-white transition-all duration-200 hover:border-blue-400"
                        />
                        <label 
                          htmlFor={`topic-${index}`} 
                          className={`text-sm cursor-pointer font-medium transition-colors duration-200 ${
                            isSelected ? 'text-blue-600' : 'text-gray-600'
                          }`}
                        >
                          {isSelected ? '已选择' : '选择'}
                        </label>
                      </div>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          );
        })}
      </div>

      {/* 一键全选按钮 - 固定在右下角 */}
      <div className="fixed bottom-8 right-8 z-50">
        <Button
          onClick={handleToggleSelectAll}
          className="w-16 h-16 rounded-full shadow-2xl hover:shadow-3xl transition-all duration-300 bg-gradient-to-r from-indigo-500 to-purple-600 hover:from-indigo-600 hover:to-purple-700 text-white border-0 hover:scale-110"
          title={isAllSelected ? "取消全选" : "全选所有选题"}
        >
          {isAllSelected ? (
            <Square className="w-6 h-6" />
          ) : (
            <CheckSquare className="w-6 h-6" />
          )}
        </Button>
      </div>

      <div className="flex flex-col items-center space-y-4 pt-8">
        {selectedTopics.length > 0 && (
          <Button
            onClick={handleGenerateSelected}
            size="lg"
            className="w-80 px-8 py-3 text-lg rounded-full shadow-lg hover:shadow-xl transition-all duration-300 bg-gradient-to-r from-purple-500 to-pink-500 hover:from-purple-600 hover:to-pink-600 text-white border-0"
          >
            <Sparkles className="w-5 h-5 mr-2" />
            生成选中的{selectedTopics.length}个选题文案
          </Button>
        )}
        
        <Button
          onClick={() => navigate(`/generate/${domain}`)}
          variant="outline"
          size="lg"
          className="w-80 px-8 py-3 text-lg rounded-full shadow-lg hover:shadow-xl transition-all duration-300 backdrop-blur-sm border-gray-200/70 hover:border-blue-300 hover:bg-blue-50/50"
        >
          <Sparkles className="w-5 h-5 mr-2" />
          跳过选题，直接生成通用文案
          <ArrowRight className="w-5 h-5 ml-2" />
        </Button>
        <p className="text-sm text-gray-500">
          或者勾选上方选题，生成对应的专门文案
        </p>
      </div>
    </div>
  );
};

export default TopicsList;
