
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Copy } from "lucide-react";
import { toast } from "@/hooks/use-toast";

interface StreamingContentCardProps {
  content: string;
  index: number;
  currentDomainInfo: {
    tag: string;
  };
  isGenerating: boolean;
  currentGeneratingIndex: number;
  totalTopics: number;
}

const StreamingContentCard = ({
  content,
  index,
  currentDomainInfo,
  isGenerating,
  currentGeneratingIndex,
  totalTopics
}: StreamingContentCardProps) => {
  const copyContent = (content: string, index: number) => {
    navigator.clipboard.writeText(content);
    toast({
      title: "复制成功",
      description: `第${index + 1}条文案已复制到剪贴板`
    });
  };

  // 判断当前卡片的状态
  const getCardStatus = () => {
    if (content) return 'completed';
    if (isGenerating && index <= currentGeneratingIndex) return 'generating';
    if (isGenerating) return 'waiting';
    return 'idle';
  };

  const cardStatus = getCardStatus();

  // 根据状态设置样式
  const getCardClassName = () => {
    const baseClass = "shadow-xl border-0 backdrop-blur-md hover:shadow-2xl transition-all duration-300 h-80 flex flex-col rounded-3xl overflow-hidden";

    switch (cardStatus) {
      case 'completed':
        return `${baseClass} bg-green-50/80 border-green-200 hover:scale-105 hover:-translate-y-1`;
      case 'generating':
        return `${baseClass} bg-blue-50/80 border-blue-200 animate-pulse`;
      case 'waiting':
        return `${baseClass} bg-gray-50/80 border-gray-200`;
      default:
        return `${baseClass} bg-white/80 hover:scale-105 hover:-translate-y-1`;
    }
  };

  return (
    <Card className={getCardClassName()}>
      <CardContent className="p-6 flex flex-col h-full">
        <div className="flex justify-between items-start mb-4 flex-shrink-0">
          <div className="flex items-center space-x-2 flex-wrap">
            <div className="bg-gradient-to-br from-purple-400 to-purple-600 text-white px-3 py-1.5 rounded-full text-xs font-medium shadow-lg">
              #{index + 1}
            </div>
            <div className="bg-gradient-to-br from-blue-400 to-blue-600 text-white px-3 py-1.5 rounded-full text-xs font-medium shadow-lg">
              #{currentDomainInfo.tag}
            </div>
            <div className="bg-gradient-to-br from-green-400 to-green-600 text-white px-3 py-1.5 rounded-full text-xs font-medium shadow-lg">
              #小红书文案
            </div>
            {/* 状态指示器 */}
            {cardStatus === 'completed' && (
              <div className="bg-gradient-to-br from-green-500 to-green-600 text-white px-2 py-1 rounded-full text-xs font-medium shadow-lg">
                ✓ 完成
              </div>
            )}
            {cardStatus === 'generating' && (
              <div className="bg-gradient-to-br from-blue-500 to-blue-600 text-white px-2 py-1 rounded-full text-xs font-medium shadow-lg animate-pulse">
                ⚡ 生成中
              </div>
            )}
            {cardStatus === 'waiting' && (
              <div className="bg-gradient-to-br from-gray-400 to-gray-500 text-white px-2 py-1 rounded-full text-xs font-medium shadow-lg">
                ⏳ 等待中
              </div>
            )}
          </div>
          <Button 
            onClick={() => copyContent(content, index)} 
            variant="outline" 
            size="sm" 
            className="flex items-center space-x-1.5 hover:bg-blue-50/80 hover:border-blue-300 text-sm px-4 py-2 h-auto flex-shrink-0 rounded-full backdrop-blur-sm border-gray-200/70 transition-all duration-200 shadow-sm hover:shadow-md"
            disabled={!content || isGenerating}
          >
            <Copy className="w-3.5 h-3.5" />
            <span>复制</span>
          </Button>
        </div>
        
        <div className="text-gray-800 flex-1 overflow-y-auto scrollbar-thin scrollbar-thumb-gray-300 scrollbar-track-gray-100">
          {content ? (
            content.split('\n').map((paragraph, idx) => {
              if (paragraph.trim()) {
                return (
                  <p key={idx} className="mb-2 last:mb-0 leading-relaxed text-sm">
                    {paragraph.trim()}
                  </p>
                );
              }
              return null;
            }).filter(Boolean)
          ) : (
            <div className="flex items-center justify-center h-full">
              {cardStatus === 'generating' ? (
                <div className="text-center">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-2"></div>
                  <p className="text-sm text-blue-600 font-medium">正在生成文案...</p>
                </div>
              ) : cardStatus === 'waiting' ? (
                <div className="text-center">
                  <div className="rounded-full h-8 w-8 border-2 border-gray-300 mx-auto mb-2 flex items-center justify-center">
                    <span className="text-gray-400 text-xs">⏳</span>
                  </div>
                  <p className="text-sm text-gray-500">等待生成中...</p>
                </div>
              ) : (
                <p className="text-sm text-gray-400">等待生成</p>
              )}
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
};

export default StreamingContentCard;
