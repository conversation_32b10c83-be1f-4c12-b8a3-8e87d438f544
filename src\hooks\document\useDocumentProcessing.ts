
import { processDocument, generatePopularizedContent } from '@/services/documentProcessor';
import { useFileValidation } from './useFileValidation';
import { DocumentSummary } from '@/types/documentConvert';

export const useDocumentProcessing = (
  state: any,
  updateState: (updates: any) => void,
  setDocumentSummary: (summary: DocumentSummary) => void,
  setPopularizedContent: (content: string) => void
) => {
  const { showErrorToast, showSuccessToast } = useFileValidation();

  const handleExtractSummary = async () => {
    if (!state.file) {
      showErrorToast(new Error("请先选择要转换的PDF或Word文档"));
      return;
    }

    updateState({ isProcessing: true, isExtractingSummary: true });
    
    try {
      const { content, summary } = await processDocument(state.file);
      
      updateState({ originalContent: content });
      setDocumentSummary(summary);
      
      // Calculate available sections
      const structuredCount = summary.structured_disputes?.length || 0;
      const legacyCount = [
        summary.案件基本情况 && summary.案件基本情况.trim().length > 10,
        summary.核心争议焦点 && summary.核心争议焦点.trim().length > 10,
        summary.法院认定与判决 && summary.法院认定与判决.trim().length > 10
      ].filter(Boolean).length;
      
      const totalSections = structuredCount > 0 ? structuredCount : (legacyCount > 0 ? legacyCount : 1);
      
      showSuccessToast(
        "文档解析成功",
        structuredCount > 0 
          ? `已提取 ${structuredCount} 个争议焦点与裁判要点，请选择需要重点呈现的内容`
          : `已提取文档的 ${totalSections} 个主要部分，请选择需要重点呈现的内容`
      );
    } catch (error) {
      console.error('提取文档摘要失败:', error);
      showErrorToast(error as Error);
    } finally {
      updateState({ isProcessing: false, isExtractingSummary: false });
    }
  };

  const handleGeneratePopularized = async () => {
    if (!state.documentSummary || state.selectedSections.length === 0) {
      showErrorToast(new Error("请至少选择一个内容部分进行科普化"));
      return;
    }

    updateState({ isGeneratingPopularized: true });
    
    try {
      const popularized = await generatePopularizedContent(
        state.documentSummary,
        state.selectedSections,
        state.originalContent,
        state.file!
      );
      
      setPopularizedContent(popularized);

      showSuccessToast(
        "科普文案生成成功",
        "已根据选定的争议焦点生成科普文案"
      );
    } catch (error) {
      console.error('生成科普文案失败:', error);
      showErrorToast(error as Error);
    } finally {
      updateState({ isGeneratingPopularized: false });
    }
  };

  return {
    handleExtractSummary,
    handleGeneratePopularized
  };
};
