
import { cleanMarkdownText } from './markdownCleaning.ts';

// Content parsing utilities
export function parseTopics(text: string): string[] {
  // Clean markdown format first
  const cleanText = cleanMarkdownText(text);
  
  const lines = cleanText.split('\n').filter(line => line.trim());
  const topics: string[] = [];
  
  // Define invalid content patterns to filter - enhanced version
  const invalidPatterns = [
    /^(好的|明白了|了解了|收到|知道了)/,
    /^AI(输出|生成|回答|示例)/,
    /^以下是/,
    /^这些(选题|话题)/,
    /^根据您的要求/,
    /^为您(生成|提供)/,
    /^选题如下/,
    /^(总结|摘要|概述)/,
    /^(注意|提示|说明)/,
    /^(用户|您可以)/,
    /^(我来|我将|让我)/,
    /^(当然|确实|的确)/,
    /^(如果|如何|怎么)/,
    /^(请|麻烦|希望)/,
    /^(可以|能够|需要)/,
    /法律(咨询|服务|建议)$/,
    /律师(事务所|咨询|服务)$/,
    /^[a-zA-Z\s]+$/,  // Pure English lines
    /^\d+[\.\s]*$/,   // Number-only lines
    // New: filter system prompt related content
    /^法律领域[：:]\s*(property|marriage|labor|debt|corporate|consumer|criminal|contract)/i,
    /^(系统|模型|提示词|调试|测试)/,
    /^(domain|topic|prompt)/i,
    /^(law|legal)\s+(field|domain|area)/i,
  ];
  
  for (const line of lines) {
    const trimmed = line.trim();
    
    // Check if it's invalid content
    const isInvalid = invalidPatterns.some(pattern => pattern.test(trimmed));
    if (isInvalid) {
      continue;
    }
    
    // Remove numbering (number + dot or just number)
    const topic = trimmed.replace(/^\d+\.?\s*/, '').trim();
    
    // Further validate topic quality
    if (topic && 
        topic.length > 10 && 
        topic.length < 200 && 
        !topic.includes('AI') && 
        !topic.includes('选题') &&
        !topic.includes('生成') &&
        !topic.includes('法律领域') &&
        !topic.includes('property') &&
        !topic.includes('domain')
    ) {
      topics.push(topic);
    }
  }
  
  return topics.slice(0, 10); // Ensure max 10 topics returned
}

export function parseGeneratedContent(text: string): string[] {
  // Clean markdown format first
  const cleanText = cleanMarkdownText(text);
  
  // Split content by separators
  const contents = cleanText.split(/---+/).filter(content => content.trim());
  
  if (contents.length === 0) {
    // If no separators, try splitting by double line breaks
    const fallbackContents = cleanText.split(/\n\s*\n/).filter(content => content.trim());
    return fallbackContents.length > 0 ? fallbackContents.slice(0, 3) : [cleanText];
  }
  
  return contents.map(content => content.trim()).slice(0, 3);
}
