import React from 'react';
interface DocumentPageTitleProps {
  children?: React.ReactNode;
}
export const DocumentPageTitle: React.FC<DocumentPageTitleProps> = ({
  children
}) => {
  return <div className="mb-12">
      {/* 页头区域 - 标题和按钮 */}
      <div className="flex items-start justify-between mb-6">
        <div className="flex-1 text-center">
          
          <p className="text-lg text-gray-600 max-w-2xl mx-auto">上传判决书，AI将提取关键内容，您可以选择重点内容进行科普化</p>
        </div>
        {/* 右侧按钮区域 */}
        {children && <div className="flex-shrink-0 ml-8 pt-2">
            {children}
          </div>}
      </div>
    </div>;
};