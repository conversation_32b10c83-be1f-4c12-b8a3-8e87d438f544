
import { toast } from "@/hooks/use-toast";
import { geminiService } from "@/services/geminiService";
import { useHistory } from "@/hooks/useHistory";
import { OptimizePageState } from './useOptimizePageState';

export const useOptimizeLogic = (
  state: OptimizePageState,
  actions: any,
  stateRef: React.MutableRefObject<any>,
  saveToLocalStorage: (data?: any) => void
) => {
  const { saveOptimizationHistory } = useHistory();

  const handleOptimize = async () => {
    if (!state.originalText.trim()) {
      toast({
        title: "请输入原始文案",
        description: "请先输入您需要优化的文案内容",
        variant: "destructive",
      });
      return;
    }

    if (state.contentTypes.length === 0) {
      toast({
        title: "请选择内容类型",
        description: "请至少选择一种生成内容类型",
        variant: "destructive",
      });
      return;
    }

    actions.setIsOptimizing(true);
    actions.setOptimizedText("");
    actions.setVideoScript("");
    
    try {
      const promises = [];
      
      // 根据选择的内容类型并行生成
      if (state.contentTypes.includes('copywriting')) {
        promises.push(
          geminiService.optimizeContent(state.originalText, state.platform, state.style)
            .then(result => ({ type: 'copywriting', content: result }))
        );
      }
      
      if (state.contentTypes.includes('video-script')) {
        promises.push(
          geminiService.generateVideoScript(state.originalText, state.platform, state.style)
            .then(result => ({ type: 'video-script', content: result }))
        );
      }

      const results = await Promise.all(promises);
      
      // 设置生成的内容
      results.forEach(result => {
        if (result.type === 'copywriting') {
          actions.setOptimizedText(result.content);
        } else if (result.type === 'video-script') {
          actions.setVideoScript(result.content);
        }
      });
      
      // 保存到历史记录（分别保存不同类型）
      const savePromises = results.map(result => {
        const contentType = result.type === 'copywriting' ? 'copywriting' : 'video-script';
        return saveOptimizationHistory(state.originalText, result.content, state.platform, state.style, contentType);
      });
      
      await Promise.all(savePromises);
      
      // 设置生成状态并记录初始文本
      actions.setHasGenerated(true);
      actions.setInitialOriginalText(state.originalText);
      
      toast({
        title: "生成完成",
        description: `已成功生成${state.contentTypes.length === 1 ? (state.contentTypes[0] === 'copywriting' ? '文案' : '短视频脚本') : '文案和短视频脚本'}`,
      });
    } catch (error) {
      toast({
        title: "生成失败",
        description: error instanceof Error ? error.message : "未知错误",
        variant: "destructive",
      });
    } finally {
      actions.setIsOptimizing(false);
    }
  };

  const handleCopyOptimizedText = () => {
    if (state.optimizedText) {
      navigator.clipboard.writeText(state.optimizedText);
      toast({
        title: "复制成功",
        description: "优化后的文案已复制到剪贴板",
      });
    }
  };

  // 编辑文案的处理函数
  const handleOptimizedTextChange = (newText: string) => {
    actions.setOptimizedText(newText);
    // 立即保存到localStorage
    const updatedData = {
      ...stateRef.current,
      optimizedText: newText
    };
    saveToLocalStorage(updatedData);
  };

  // 编辑短视频脚本的处理函数
  const handleVideoScriptChange = (newScript: string) => {
    actions.setVideoScript(newScript);
    // 立即保存到localStorage
    const updatedData = {
      ...stateRef.current,
      videoScript: newScript
    };
    saveToLocalStorage(updatedData);
  };

  return {
    handleOptimize,
    handleCopyOptimizedText,
    handleOptimizedTextChange,
    handleVideoScriptChange
  };
};
