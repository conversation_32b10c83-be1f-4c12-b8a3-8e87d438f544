const { 
  generateTopicsPrompt, 
  optimizeContentPrompt, 
  generateContentPrompt, 
  generateContentByTopicPrompt, 
  popularizeContentPrompt, 
  extractDocumentSummaryPrompt,
  generateVideoScriptPrompt
} = require('./prompts');
const { parseTopics, parseGeneratedContent, cleanXiaohongshuContent, cleanAIGeneratedContent } = require('./textProcessing');

class DoubaoService {
  constructor() {
    // 使用提供的API密钥，如果环境变量不存在则使用默认值
    this.apiKey = process.env.DOUBAO_API_KEY || 'df725996-30e3-4769-84f1-8c3737dccc58';
    this.baseUrl = 'https://ark.cn-beijing.volces.com/api/v3/chat/completions';
    this.model = 'doubao-seed-1-6-thinking-250615';

    console.log('豆包服务初始化:', {
      hasApiKey: !!this.apiKey,
      apiKeyPrefix: this.apiKey ? this.apiKey.substring(0, 8) + '...' : 'none',
      baseUrl: this.baseUrl,
      model: this.model
    });
  }

  async callDoubaoAPI(input) {
    try {
      const requestBody = {
        model: this.model,
        messages: [
          {
            role: "user",
            content: input
          }
        ],
        temperature: 0.7,
        max_tokens: 4000
      };
      
      console.log('调用豆包API，请求体:', JSON.stringify(requestBody, null, 2));
      
      const fetch = require('node-fetch');
      const response = await fetch(this.baseUrl, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${this.apiKey}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(requestBody)
      });

      console.log('豆包API响应状态:', response.status);
      
      if (!response.ok) {
        const errorText = await response.text();
        console.error('豆包API错误:', response.status, errorText);
        throw new Error(`豆包API错误: ${response.status} ${errorText}`);
      }

      const result = await response.json();
      console.log('豆包API响应:', JSON.stringify(result, null, 2));
      
      // 从Chat API的响应中提取文本内容
      let content = '';
      if (result.choices && Array.isArray(result.choices) && result.choices.length > 0) {
        const choice = result.choices[0];
        if (choice.message && choice.message.content) {
          content = choice.message.content;
        }
      }
      
      console.log('提取的内容:', content);
      return content || '';
    } catch (error) {
      console.error('调用豆包API时出错:', error);
      throw new Error(`豆包API调用失败: ${error.message}`);
    }
  }

  async generateTopics(domain) {
    try {
      const prompt = generateTopicsPrompt(domain);
      console.log('生成选题，领域:', domain);

      const rawText = await this.callDoubaoAPI(prompt);
      console.log('选题原始响应:', rawText.substring(0, 200) + '...');
      
      const topics = parseTopics(rawText);
      console.log('解析出的选题:', topics.length, '个');
      
      return topics;
    } catch (error) {
      console.error('生成选题时出错:', error);
      throw new Error(`生成选题失败: ${error.message}`);
    }
  }

  async optimizeContent(text, platform, style) {
    try {
      const prompt = optimizeContentPrompt(text, platform, style);
      console.log('优化内容，长度:', text.length);

      const optimizedText = await this.callDoubaoAPI(prompt);
      const cleanedText = cleanAIGeneratedContent(optimizedText);
      console.log('优化后内容长度:', cleanedText.length);
      
      return cleanedText;
    } catch (error) {
      console.error('优化内容时出错:', error);
      throw new Error(`优化内容失败: ${error.message}`);
    }
  }

  async generateVideoScript(text, platform, style) {
    try {
      const prompt = generateVideoScriptPrompt(text, platform, style);
      console.log('生成短视频脚本，长度:', text.length);

      const videoScript = await this.callDoubaoAPI(prompt);
      const cleanedScript = cleanAIGeneratedContent(videoScript);
      console.log('生成的短视频脚本长度:', cleanedScript.length);
      
      return cleanedScript;
    } catch (error) {
      console.error('生成短视频脚本时出错:', error);
      throw new Error(`生成短视频脚本失败: ${error.message}`);
    }
  }

  async generateContentByDomain(domain) {
    try {
      const prompt = generateContentPrompt(domain);
      console.log('根据领域生成内容:', domain);

      const rawText = await this.callDoubaoAPI(prompt);
      console.log('内容生成原始响应:', rawText.substring(0, 200) + '...');
      
      const contents = parseGeneratedContent(rawText);
      console.log('生成的内容数量:', contents.length);
      
      return contents.map(content => cleanAIGeneratedContent(content));
    } catch (error) {
      console.error('根据领域生成内容时出错:', error);
      throw new Error(`生成内容失败: ${error.message}`);
    }
  }

  async generateContentByTopic(topic) {
    try {
      const prompt = generateContentByTopicPrompt(topic);
      console.log('根据主题生成内容:', topic);

      const rawText = await this.callDoubaoAPI(prompt);
      console.log('主题内容原始响应:', rawText.substring(0, 200) + '...');
      
      // 对于小红书文案，使用专门的清理函数
      const cleanedContent = cleanXiaohongshuContent(rawText);
      console.log('清理后的小红书内容长度:', cleanedContent.length);
      
      // 返回单条文案内容
      return [cleanedContent];
    } catch (error) {
      console.error('根据主题生成内容时出错:', error);
      throw new Error(`生成主题内容失败: ${error.message}`);
    }
  }

  async popularizeContent(content) {
    try {
      const prompt = popularizeContentPrompt(content);
      console.log('科普化内容，长度:', content.length);

      const popularizedText = await this.callDoubaoAPI(prompt);
      const cleanedText = cleanAIGeneratedContent(popularizedText);
      console.log('科普化后内容长度:', cleanedText.length);
      
      return cleanedText;
    } catch (error) {
      console.error('科普化内容时出错:', error);
      throw new Error(`科普化内容失败: ${error.message}`);
    }
  }

  async extractDocumentSummary(content) {
    try {
      const prompt = extractDocumentSummaryPrompt(content);
      console.log('提取文档摘要，内容长度:', content.length);

      const summaryText = await this.callDoubaoAPI(prompt);
      console.log('文档摘要长度:', summaryText.length);
      
      // 解析结构化摘要格式
      const summary = this.parseStructuredSummary(summaryText);
      
      return summary;
    } catch (error) {
      console.error('提取文档摘要时出错:', error);
      throw new Error(`提取文档摘要失败: ${error.message}`);
    }
  }

  parseStructuredSummary(summaryText) {
    // 检查是否为错误情况
    if (summaryText.includes('未能从文书中提炼出完整的争议焦点与裁判要点')) {
      return {
        error: '文档内容不足，无法提取有效的争议焦点与裁判要点',
        suggestion: '请确认文档是否为完整的判决书或包含足够的法律分析内容'
      };
    }

    // 解析结构化列表
    const disputePoints = [];
    let basicInfo = '';
    let legalAnalysis = '';
    let socialImpact = '';

    // 匹配格式：数字. [类型] 争议描述 [裁判要点] 要点内容
    const listPattern = /(\d+)\.\s*\[([^\]]+)\]\s*([^\n\[]+)\s*\[裁判要点\]\s*([^\n]+(?:\n(?!\d+\.)[^\n]*)*)/g;
    
    let match;
    while ((match = listPattern.exec(summaryText)) !== null) {
      const [, number, disputeType, disputeDescription, rulingPoint] = match;
      
      disputePoints.push({
        序号: parseInt(number),
        争议类型: disputeType.trim(),
        争议描述: disputeDescription.trim(),
        裁判要点: rulingPoint.trim().replace(/\n\s*/g, ' ')
      });
    }

    // 如果没有匹配到结构化格式，尝试简单分段处理
    if (disputePoints.length === 0) {
      const sections = summaryText.split(/\n\s*\n/).filter(section => section.trim());
      if (sections.length >= 3) {
        basicInfo = sections[0].trim();
        legalAnalysis = sections[1].trim();
        socialImpact = sections[2].trim();
      } else if (sections.length > 0) {
        basicInfo = summaryText.trim();
      }
    } else {
      // 将争议点转换为原有的格式以保持兼容性
      basicInfo = disputePoints.filter(p => p.争议类型 === '事实争议')
        .map(p => `${p.争议描述} ${p.裁判要点}`).join('\n\n');
      
      legalAnalysis = disputePoints.filter(p => p.争议类型 === '法律争议')
        .map(p => `${p.争议描述} ${p.裁判要点}`).join('\n\n');
      
      // 如果没有明确分类，将所有争议点放入法律分析
      if (!basicInfo && disputePoints.length > 0) {
        legalAnalysis = disputePoints.map(p => `${p.争议描述} ${p.裁判要点}`).join('\n\n');
      }
      
      socialImpact = '本案通过法院的细致审理和精准认定，为类似争议的解决提供了重要的法律参考和实践指导。';
    }

    return {
      story: basicInfo || '案件基本情况暂无详细信息',
      legal_analysis: legalAnalysis || '法律争议分析暂无详细信息', 
      social_impact: socialImpact || '社会影响分析暂无详细信息',
      raw_summary: summaryText,
      structured_disputes: disputePoints // 保留结构化数据供后续使用
    };
  }
}

module.exports = DoubaoService;