
import { Card, CardContent } from "@/components/ui/card";
import { useState } from "react";
import { useNavigate } from "react-router-dom";
import { Scale, Heart, Briefcase, CreditCard, Home, Building, Shield, FileText, ArrowLeft } from "lucide-react";

const Domains = () => {
  const [selectedDomain, setSelectedDomain] = useState<string | null>(null);
  const navigate = useNavigate();

  const domains = [
    {
      id: "marriage",
      title: "婚姻家庭",
      description: "离婚纠纷、财产分割、子女抚养等",
      icon: Heart,
      color: "bg-gradient-to-br from-pink-400 to-pink-600",
      gradient: "from-pink-50/80 via-rose-50/60 to-pink-100/40"
    },
    {
      id: "labor",
      title: "劳动用工",
      description: "劳动合同、工资纠纷、工伤赔偿等",
      icon: Briefcase,
      color: "bg-gradient-to-br from-blue-400 to-blue-600",
      gradient: "from-blue-50/80 via-indigo-50/60 to-blue-100/40"
    },
    {
      id: "debt",
      title: "债务纠纷",
      description: "借贷纠纷、欠款追讨、担保责任等",
      icon: CreditCard,
      color: "bg-gradient-to-br from-orange-400 to-orange-600",
      gradient: "from-orange-50/80 via-amber-50/60 to-orange-100/40"
    },
    {
      id: "property",
      title: "房产纠纷",
      description: "买卖合同、租赁纠纷、产权争议等",
      icon: Home,
      color: "bg-gradient-to-br from-green-400 to-green-600",
      gradient: "from-green-50/80 via-emerald-50/60 to-green-100/40"
    },
    {
      id: "corporate",
      title: "公司法务",
      description: "公司设立、股权纠纷、合规咨询等",
      icon: Building,
      color: "bg-gradient-to-br from-purple-400 to-purple-600",
      gradient: "from-purple-50/80 via-violet-50/60 to-purple-100/40"
    },
    {
      id: "consumer",
      title: "消费维权",
      description: "产品质量、服务纠纷、退货维权等",
      icon: Shield,
      color: "bg-gradient-to-br from-red-400 to-red-600",
      gradient: "from-red-50/80 via-pink-50/60 to-red-100/40"
    },
    {
      id: "criminal",
      title: "刑事辩护",
      description: "刑事案件辩护、取保候审等",
      icon: Scale,
      color: "bg-gradient-to-br from-gray-500 to-gray-700",
      gradient: "from-gray-50/80 via-slate-50/60 to-gray-100/40"
    },
    {
      id: "contract",
      title: "合同审查",
      description: "合同起草、审查、纠纷处理等",
      icon: FileText,
      color: "bg-gradient-to-br from-indigo-400 to-indigo-600",
      gradient: "from-indigo-50/80 via-blue-50/60 to-indigo-100/40"
    }
  ];

  const handleDomainSelect = (domainId: string) => {
    setSelectedDomain(domainId);
    // 延迟跳转，给用户视觉反馈
    setTimeout(() => {
      navigate(`/topics/${domainId}`);
    }, 300);
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50/70 via-white to-indigo-50/70">
      {/* Header */}
      <div className="bg-white/80 backdrop-blur-sm border-b border-gray-200/50 shadow-sm">
        <div className="max-w-6xl mx-auto px-6 py-6">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <div className="bg-gradient-to-br from-blue-500 to-blue-700 p-3 rounded-2xl shadow-lg">
                <Scale className="h-7 w-7 text-white" />
              </div>
              <div>
                <h1 className="text-2xl font-bold text-gray-900">法律文案助手</h1>
                <p className="text-sm text-gray-600 mt-1">为法律专业人士设计</p>
              </div>
            </div>
            <button
              onClick={() => navigate("/content-generate")}
              className="flex items-center space-x-2 text-gray-600 hover:text-gray-900 transition-all duration-200 px-4 py-2 rounded-xl hover:bg-gray-100/60 backdrop-blur-sm"
            >
              <ArrowLeft className="w-4 h-4" />
              <span>返回内容生成</span>
            </button>
          </div>
        </div>
      </div>

      <div className="max-w-6xl mx-auto px-6 py-12">
        <div className="text-center mb-12">
          <h2 className="text-3xl font-bold text-gray-900 mb-4">选择法律领域</h2>
          <p className="text-lg text-gray-600">请选择您需要生成内容的专业领域</p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
          {domains.map((domain) => {
            const IconComponent = domain.icon;
            const isSelected = selectedDomain === domain.id;
            
            return (
              <Card
                key={domain.id}
                className={`cursor-pointer transition-all duration-300 hover:shadow-2xl transform hover:scale-105 hover:-translate-y-2 border-0 bg-gradient-to-br ${domain.gradient} backdrop-blur-sm rounded-3xl overflow-hidden ${
                  isSelected ? "ring-4 ring-blue-400/50 scale-105 shadow-2xl" : "shadow-lg"
                }`}
                onClick={() => handleDomainSelect(domain.id)}
              >
                <CardContent className="p-8 text-center h-full flex flex-col justify-between">
                  <div>
                    <div className={`w-20 h-20 ${domain.color} rounded-full flex items-center justify-center mx-auto mb-6 shadow-xl transition-transform duration-300 hover:scale-110`}>
                      <IconComponent className="w-10 h-10 text-white" />
                    </div>
                    <h3 className="text-xl font-bold text-gray-900 mb-3">{domain.title}</h3>
                    <p className="text-sm text-gray-600 leading-relaxed">{domain.description}</p>
                  </div>
                </CardContent>
              </Card>
            );
          })}
        </div>
      </div>
    </div>
  );
};

export default Domains;
