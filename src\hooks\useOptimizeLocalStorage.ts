
import { useEffect, useRef } from 'react';
import { OptimizePageState } from './useOptimizePageState';

// localStorage keys
const STORAGE_KEYS = {
  ORIGINAL_TEXT: 'optimization_original_text',
  OPTIMIZED_TEXT: 'optimization_optimized_text',
  VIDEO_SCRIPT: 'optimization_video_script',
  PLATFORM: 'optimization_platform',
  STYLE: 'optimization_style',
  CONTENT_TYPES: 'optimization_content_types'
};

export const useOptimizeLocalStorage = (
  state: OptimizePageState,
  actions: any,
  stateRef: React.MutableRefObject<any>
) => {
  const hasLoadedFromStorage = useRef(false);

  // 页面加载时恢复保存的内容（只执行一次）
  useEffect(() => {
    if (hasLoadedFromStorage.current) return;
    
    try {
      const savedOriginalText = localStorage.getItem(STORAGE_KEYS.ORIGINAL_TEXT) || "";
      const savedOptimizedText = localStorage.getItem(STORAGE_KEYS.OPTIMIZED_TEXT) || "";
      const savedVideoScript = localStorage.getItem(STORAGE_KEYS.VIDEO_SCRIPT) || "";
      const savedPlatform = localStorage.getItem(STORAGE_KEYS.PLATFORM) || "";
      const savedStyle = localStorage.getItem(STORAGE_KEYS.STYLE) || "xiaohongshu";
      const savedContentTypes = JSON.parse(localStorage.getItem(STORAGE_KEYS.CONTENT_TYPES) || '["copywriting"]');

      if (savedOriginalText || savedOptimizedText || savedVideoScript || savedPlatform || savedStyle !== "xiaohongshu") {
        actions.setOriginalText(savedOriginalText);
        actions.setOptimizedText(savedOptimizedText);
        actions.setVideoScript(savedVideoScript);
        actions.setPlatform(savedPlatform);
        actions.setStyle(savedStyle);
        actions.setContentTypes(savedContentTypes);
        actions.setInitialOriginalText(savedOriginalText);
        
        // 如果有生成的内容，则设置已生成状态
        if (savedOptimizedText || savedVideoScript) {
          actions.setHasGenerated(true);
        }
        
        console.log('从localStorage恢复数据成功:', {
          originalTextLength: savedOriginalText.length,
          optimizedTextLength: savedOptimizedText.length,
          videoScriptLength: savedVideoScript.length,
          platform: savedPlatform,
          style: savedStyle,
          contentTypes: savedContentTypes
        });
      }
      
      hasLoadedFromStorage.current = true;
    } catch (error) {
      console.error('从localStorage恢复数据失败:', error);
      hasLoadedFromStorage.current = true;
    }
  }, []); // 空依赖数组，只在组件挂载时执行一次

  // 保存数据到localStorage的函数
  const saveToLocalStorage = (data?: {
    originalText: string;
    optimizedText: string;
    videoScript: string;
    platform: string;
    style: string;
    contentTypes: string[];
  }) => {
    try {
      const saveData = data || stateRef.current;
      localStorage.setItem(STORAGE_KEYS.ORIGINAL_TEXT, saveData.originalText);
      localStorage.setItem(STORAGE_KEYS.OPTIMIZED_TEXT, saveData.optimizedText);
      localStorage.setItem(STORAGE_KEYS.VIDEO_SCRIPT, saveData.videoScript);
      localStorage.setItem(STORAGE_KEYS.PLATFORM, saveData.platform);
      localStorage.setItem(STORAGE_KEYS.STYLE, saveData.style);
      localStorage.setItem(STORAGE_KEYS.CONTENT_TYPES, JSON.stringify(saveData.contentTypes));
    } catch (error) {
      console.error('保存数据到localStorage失败:', error);
    }
  };

  // 监听原始文本变化，判断是否需要重置按钮状态
  useEffect(() => {
    if (!hasLoadedFromStorage.current) return;
    
    if (state.hasGenerated && state.originalText !== state.initialOriginalText) {
      actions.setHasGenerated(false);
    }
  }, [state.originalText, state.initialOriginalText, state.hasGenerated]);

  // 防抖保存到localStorage
  useEffect(() => {
    if (!hasLoadedFromStorage.current) return;
    
    const timeoutId = setTimeout(() => {
      if (state.originalText || state.optimizedText || state.videoScript || state.platform || state.style !== "xiaohongshu") {
        saveToLocalStorage();
      }
    }, 1000);

    return () => clearTimeout(timeoutId);
  }, [state.originalText, state.optimizedText, state.videoScript, state.platform, state.style, state.contentTypes]);

  // 页面离开时保存数据
  useEffect(() => {
    const handleBeforeUnload = () => {
      if (hasLoadedFromStorage.current) {
        saveToLocalStorage();
      }
    };

    const handleVisibilityChange = () => {
      if (document.visibilityState === 'hidden' && hasLoadedFromStorage.current) {
        saveToLocalStorage();
      }
    };

    window.addEventListener('beforeunload', handleBeforeUnload);
    document.addEventListener('visibilitychange', handleVisibilityChange);

    return () => {
      window.removeEventListener('beforeunload', handleBeforeUnload);
      document.removeEventListener('visibilitychange', handleVisibilityChange);
    };
  }, []);

  return { saveToLocalStorage };
};
