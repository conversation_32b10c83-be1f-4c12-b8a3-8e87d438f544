
import React from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { History } from 'lucide-react';
import HistoryItem from './HistoryItem';
import { HistoryItem as HistoryItemType, OptimizationHistory } from '@/hooks/useHistory';

interface HistoryPanelProps {
  historyItems: HistoryItemType[];
  isLoadingHistory: boolean;
  onRefresh: () => void;
  onClose: () => void;
  onLoadOptimization: (item: OptimizationHistory) => void;
  onCopyContent: (content: string, type: string) => void;
  onDeleteItem: (item: HistoryItemType) => void;
}

const HistoryPanel: React.FC<HistoryPanelProps> = ({
  historyItems,
  isLoadingHistory,
  onRefresh,
  onClose,
  onLoadOptimization,
  onCopyContent,
  onDeleteItem,
}) => {
  return (
    <Card className="mb-8 shadow-xl border-0 bg-white/90 backdrop-blur-lg rounded-3xl overflow-hidden">
      <CardContent className="p-8">
        <div className="flex items-center justify-between mb-6">
          <h3 className="text-xl font-semibold text-gray-900">
            历史记录 ({historyItems.length})
          </h3>
          <div className="flex items-center space-x-3">
            <Button
              onClick={() => {
                console.log('手动刷新历史记录');
                onRefresh();
              }}
              size="sm"
              variant="ghost"
              className="rounded-xl hover:bg-gray-50/80 transition-all duration-200"
            >
              刷新
            </Button>
            <Button
              onClick={onClose}
              variant="ghost"
              size="sm"
              className="rounded-xl hover:bg-gray-50/80 transition-all duration-200"
            >
              关闭
            </Button>
          </div>
        </div>
        
        {isLoadingHistory ? (
          <div className="text-center py-12">
            <div className="animate-spin rounded-full h-10 w-10 border-b-2 border-blue-600 mx-auto"></div>
            <p className="text-gray-600 mt-3">加载中...</p>
          </div>
        ) : historyItems.length === 0 ? (
          <div className="text-center py-12 text-gray-500">
            <div className="bg-gray-50 rounded-2xl p-8 max-w-md mx-auto">
              <History className="w-12 h-12 text-gray-300 mx-auto mb-4" />
              <p className="text-lg">暂无历史记录</p>
              <p className="text-sm text-gray-400 mt-2">开始使用优化或生成功能来创建历史记录</p>
            </div>
          </div>
        ) : (
          <div className="space-y-4 max-h-96 overflow-y-auto scrollbar-thin scrollbar-thumb-gray-300 scrollbar-track-gray-100">
            {historyItems.map((item, index) => (
              <HistoryItem
                key={`${item.type}-${item.id}-${index}`}
                item={item}
                index={index}
                onLoad={onLoadOptimization}
                onCopy={onCopyContent}
                onDelete={onDeleteItem}
              />
            ))}
          </div>
        )}
      </CardContent>
    </Card>
  );
};

export default HistoryPanel;
