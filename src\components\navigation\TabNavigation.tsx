
import React from 'react';
import { Sparkles, FileText } from 'lucide-react';
import { useIsMobile } from '@/hooks/use-mobile';

interface TabNavigationProps {
  activeTab: string;
  onTabChange: (tab: string) => void;
}

const TabNavigation: React.FC<TabNavigationProps> = ({ activeTab, onTabChange }) => {
  const isMobile = useIsMobile();

  if (isMobile) {
    return (
      <div className="flex space-x-1 bg-gray-100/70 backdrop-blur-sm rounded-xl p-1 mb-6 mx-4 shadow-inner">
        <button
          onClick={() => onTabChange("optimize")}
          className={`flex-1 py-3 px-4 rounded-lg text-sm font-medium transition-all duration-300 ${
            activeTab === "optimize"
              ? "bg-white text-blue-600 shadow-lg transform scale-[1.02]"
              : "text-gray-600 hover:text-gray-900 hover:bg-white/50"
          }`}
        >
          <Sparkles className="w-4 h-4 inline mr-2" />
          内容优化
        </button>
        <button
          onClick={() => onTabChange("generate")}
          className={`flex-1 py-3 px-4 rounded-lg text-sm font-medium transition-all duration-300 ${
            activeTab === "generate"
              ? "bg-white text-blue-600 shadow-lg transform scale-[1.02]"
              : "text-gray-600 hover:text-gray-900 hover:bg-white/50"
          }`}
        >
          <FileText className="w-4 h-4 inline mr-2" />
          内容生成
        </button>
      </div>
    );
  }

  // PC端保持原有设计
  return (
    <div className="flex space-x-2 bg-gray-100/70 backdrop-blur-sm rounded-2xl p-2 mb-10 max-w-md mx-auto shadow-inner">
      <button
        onClick={() => onTabChange("optimize")}
        className={`flex-1 py-3 px-6 rounded-xl text-sm font-medium transition-all duration-300 ${
          activeTab === "optimize"
            ? "bg-white text-blue-600 shadow-lg transform scale-[1.02]"
            : "text-gray-600 hover:text-gray-900 hover:bg-white/50"
        }`}
      >
        <Sparkles className="w-4 h-4 inline mr-2" />
        内容优化
      </button>
      <button
        onClick={() => onTabChange("generate")}
        className={`flex-1 py-3 px-6 rounded-xl text-sm font-medium transition-all duration-300 ${
          activeTab === "generate"
            ? "bg-white text-blue-600 shadow-lg transform scale-[1.02]"
            : "text-gray-600 hover:text-gray-900 hover:bg-white/50"
        }`}
      >
        <FileText className="w-4 h-4 inline mr-2" />
        内容生成
      </button>
    </div>
  );
};

export default TabNavigation;
