
import React from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, SheetPortal } from '@/components/ui/sheet';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { <PERSON><PERSON>, FileText, Sparkles, Trash2, RefreshCw } from 'lucide-react';
import { DocumentHistoryItem } from '@/hooks/useDocumentHistory';
import { ScrollArea } from '@/components/ui/scroll-area';
import * as SheetPrimitive from "@radix-ui/react-dialog"

interface DocumentHistoryPanelProps {
  historyItems: DocumentHistoryItem[];
  isLoading: boolean;
  isOpen: boolean;
  onRefresh: () => void;
  onClose: () => void;
  onCopyContent: (content: string, type: string) => void;
  onDeleteItem: (itemId: string) => void;
}

export const DocumentHistoryPanel: React.FC<DocumentHistoryPanelProps> = ({
  historyItems,
  isLoading,
  isOpen,
  onRefresh,
  onClose,
  onCopyContent,
  onDeleteItem
}) => {
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const truncateText = (text: string, maxLength: number = 100) => {
    return text.length > maxLength ? text.substring(0, maxLength) + '...' : text;
  };

  return (
    <SheetPrimitive.Root open={isOpen} onOpenChange={onClose}>
      <SheetPortal>
        <SheetPrimitive.Overlay className="fixed inset-0 z-50 bg-black/20 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0" />
        <SheetPrimitive.Content className="fixed z-50 gap-4 bg-background p-6 shadow-lg transition ease-in-out data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:duration-300 data-[state=open]:duration-500 inset-y-0 right-0 h-full w-full data-[state=closed]:slide-out-to-right data-[state=open]:slide-in-from-right sm:max-w-2xl bg-gradient-to-br from-blue-50/70 via-white to-indigo-50/70 flex flex-col rounded-l-3xl">
          <SheetHeader className="pb-6 flex-shrink-0">
            <div className="flex items-center justify-between">
              <SheetTitle className="text-xl font-semibold text-gray-900 flex items-center space-x-2">
                <div className="w-8 h-8 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-lg flex items-center justify-center">
                  <FileText className="w-4 h-4 text-white" />
                </div>
                <span>文档转换历史</span>
              </SheetTitle>
            </div>
            <p className="text-sm text-gray-600 mt-2">
              显示您的文档转换历史记录
            </p>
            <div className="flex justify-start mt-4">
              <Button
                variant="outline"
                size="sm"
                onClick={onRefresh}
                disabled={isLoading}
                className="flex items-center space-x-2 rounded-xl bg-white/80 hover:bg-gray-50/80 border-gray-200/70 hover:border-blue-300 transition-all duration-200"
              >
                <RefreshCw className={`w-4 h-4 ${isLoading ? 'animate-spin' : ''}`} />
                <span>刷新</span>
              </Button>
            </div>
          </SheetHeader>

          <ScrollArea className="flex-1 pr-2">
            {isLoading ? (
              <div className="text-center py-8">
                <div className="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
                <p className="mt-4 text-gray-600">加载历史记录中...</p>
              </div>
            ) : historyItems.length === 0 ? (
              <div className="text-center py-8">
                <FileText className="w-16 h-16 text-gray-400 mx-auto mb-4" />
                <p className="text-gray-600">暂无转换历史记录</p>
              </div>
            ) : (
              <div className="space-y-4">
                {historyItems.map((item) => (
                  <Card key={item.id} className="bg-white/80 backdrop-blur-sm shadow-md border-0 rounded-2xl">
                    <CardHeader className="pb-3">
                      <CardTitle className="flex items-center justify-between">
                        <div className="flex items-center space-x-3">
                          <div className="p-2 rounded-lg bg-gradient-to-br from-blue-500 to-blue-600">
                            <FileText className="w-4 h-4 text-white" />
                          </div>
                          <div>
                            <span className="text-lg font-medium">{item.original_filename}</span>
                            <p className="text-sm text-gray-500">{formatDate(item.created_at)}</p>
                          </div>
                        </div>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => onDeleteItem(item.id)}
                          className="text-red-500 hover:text-red-700 hover:bg-red-50 rounded-xl"
                        >
                          <Trash2 className="w-4 h-4" />
                        </Button>
                      </CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-4">
                      {/* 科普文案 */}
                      <div className="bg-gradient-to-br from-green-50 to-emerald-50 border border-green-200 rounded-xl p-4">
                        <div className="flex items-center justify-between mb-2">
                          <div className="flex items-center space-x-2">
                            <Sparkles className="w-4 h-4 text-green-600" />
                            <span className="font-medium text-green-800">科普文案</span>
                          </div>
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => onCopyContent(item.popularized_content, '科普文案')}
                            className="group relative overflow-hidden bg-gradient-to-r from-blue-50 to-blue-100 border-2 border-blue-200 hover:border-blue-300 text-blue-700 hover:text-blue-800 rounded-xl px-4 py-1 transition-all duration-300 hover:shadow-lg hover:shadow-blue-200/50"
                          >
                            <Copy className="w-3 h-3" />
                          </Button>
                        </div>
                        <p className="text-sm text-gray-700 leading-relaxed">
                          {truncateText(item.popularized_content)}
                        </p>
                      </div>

                      {/* 原始内容 */}
                      <div className="bg-gradient-to-br from-gray-50 to-gray-100 border border-gray-200 rounded-xl p-4">
                        <div className="flex items-center justify-between mb-2">
                          <div className="flex items-center space-x-2">
                            <FileText className="w-4 h-4 text-gray-600" />
                            <span className="font-medium text-gray-800">原始内容</span>
                          </div>
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => onCopyContent(item.original_content, '原始内容')}
                            className="group relative overflow-hidden bg-gradient-to-r from-blue-50 to-blue-100 border-2 border-blue-200 hover:border-blue-300 text-blue-700 hover:text-blue-800 rounded-xl px-4 py-1 transition-all duration-300 hover:shadow-lg hover:shadow-blue-200/50"
                          >
                            <Copy className="w-3 h-3" />
                          </Button>
                        </div>
                        <p className="text-sm text-gray-700 leading-relaxed">
                          {truncateText(item.original_content)}
                        </p>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            )}
          </ScrollArea>
          
          <SheetPrimitive.Close className="absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-secondary">
            <span className="sr-only">Close</span>
          </SheetPrimitive.Close>
        </SheetPrimitive.Content>
      </SheetPortal>
    </SheetPrimitive.Root>
  );
};
