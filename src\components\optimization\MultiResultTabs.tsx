
import React from 'react';
import { toast } from '@/hooks/use-toast';
import SingleContentResult from './SingleContentResult';
import MultiContentTabs from './MultiContentTabs';
import { useOptimizationHistory } from '@/hooks/useOptimizationHistory';

interface MultiResultTabsProps {
  results: {
    copywriting?: string;
    videoScript?: string;
  };
  isGenerating: boolean;
  selectedTypes: string[];
  originalText: string;
  onCopywritingChange?: (content: string) => void;
  onVideoScriptChange?: (content: string) => void;
}

const MultiResultTabs: React.FC<MultiResultTabsProps> = ({
  results,
  isGenerating,
  selectedTypes,
  originalText,
  onCopywritingChange,
  onVideoScriptChange
}) => {
  // 使用历史记录 hook 获取不同内容类型的多个版本
  const {
    copywritingResults,
    videoScriptResults,
    currentCopywritingIndex,
    currentVideoScriptIndex,
    handlePreviousCopywriting,
    handleNextCopywriting,
    handlePreviousVideoScript,
    handleNextVideoScript,
    getCurrentCopywritingText,
    getCurrentVideoScriptText
  } = useOptimizationHistory(originalText, results.copywriting || '', results.videoScript || '', isGenerating);

  const handleCopyCopywriting = () => {
    const textToCopy = getCurrentCopywritingText();
    if (textToCopy) {
      navigator.clipboard.writeText(textToCopy);
      toast({
        title: "复制成功",
        description: "文案已复制到剪贴板",
      });
    }
  };

  const handleCopyVideoScript = () => {
    const textToCopy = getCurrentVideoScriptText();
    if (textToCopy) {
      navigator.clipboard.writeText(textToCopy);
      toast({
        title: "复制成功",
        description: "短视频脚本已复制到剪贴板",
      });
    }
  };

  if (selectedTypes.length <= 1) {
    // 单个类型时直接显示内容
    const contentType = selectedTypes[0] || 'copywriting';
    const content = contentType === 'copywriting' ? getCurrentCopywritingText() : getCurrentVideoScriptText();
    const currentIndex = contentType === 'copywriting' ? currentCopywritingIndex : currentVideoScriptIndex;
    const totalCount = contentType === 'copywriting' ? copywritingResults.length : videoScriptResults.length;
    
    return (
      <SingleContentResult
        contentType={contentType}
        content={content}
        isGenerating={isGenerating}
        currentIndex={currentIndex}
        totalCount={totalCount}
        onPrevious={contentType === 'copywriting' ? handlePreviousCopywriting : handlePreviousVideoScript}
        onNext={contentType === 'copywriting' ? handleNextCopywriting : handleNextVideoScript}
        onContentChange={contentType === 'copywriting' ? onCopywritingChange! : onVideoScriptChange!}
        onCopy={contentType === 'copywriting' ? handleCopyCopywriting : handleCopyVideoScript}
      />
    );
  }

  // 多个类型时显示标签页
  return (
    <MultiContentTabs
      selectedTypes={selectedTypes}
      copywritingContent={getCurrentCopywritingText()}
      videoScriptContent={getCurrentVideoScriptText()}
      isGenerating={isGenerating}
      copywritingIndex={currentCopywritingIndex}
      copywritingTotal={copywritingResults.length}
      videoScriptIndex={currentVideoScriptIndex}
      videoScriptTotal={videoScriptResults.length}
      onCopywritingPrevious={handlePreviousCopywriting}
      onCopywritingNext={handleNextCopywriting}
      onVideoScriptPrevious={handlePreviousVideoScript}
      onVideoScriptNext={handleNextVideoScript}
      onCopywritingChange={onCopywritingChange!}
      onVideoScriptChange={onVideoScriptChange!}
      onCopyCopywriting={handleCopyCopywriting}
      onCopyVideoScript={handleCopyVideoScript}
    />
  );
};

export default MultiResultTabs;
