
import React from 'react';
import { Button } from '@/components/ui/button';
import { ChevronLeft, ChevronRight } from 'lucide-react';

interface VersionNavigationProps {
  currentIndex: number;
  totalCount: number;
  onPrevious: () => void;
  onNext: () => void;
}

const VersionNavigation: React.FC<VersionNavigationProps> = ({
  currentIndex,
  totalCount,
  onPrevious,
  onNext
}) => {
  if (totalCount <= 1) {
    return null;
  }

  return (
    <div className="flex items-center space-x-1 text-sm text-gray-500">
      <Button
        onClick={onPrevious}
        disabled={currentIndex === 0}
        variant="ghost"
        size="sm"
        className="h-6 w-6 p-0"
      >
        <ChevronLeft className="w-3 h-3" />
      </Button>
      
      <span className="text-xs font-medium min-w-[20px] text-center">
        {currentIndex + 1}/{totalCount}
      </span>
      
      <Button
        onClick={onNext}
        disabled={currentIndex === totalCount - 1}
        variant="ghost"
        size="sm"
        className="h-6 w-6 p-0"
      >
        <ChevronRight className="w-3 h-3" />
      </Button>
    </div>
  );
};

export default VersionNavigation;
