import React from 'react';
import { useNavigate } from 'react-router-dom';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Scale, Sparkles, FileText, BookOpen, ArrowLeft, ChevronRight } from 'lucide-react';

const Demo = () => {
  const navigate = useNavigate();

  const demoData = {
    optimize: {
      title: "内容优化",
      description: "将专业法律术语转化为易懂的朋友圈内容",
      icon: Sparkles,
      color: "from-blue-500 to-blue-600",
      bgColor: "from-blue-50/80 via-indigo-50/60 to-blue-100/40",
      examples: [
        {
          title: "示例一：输入通用法律概念",
          input: "劳动合同中的试用期",
          output: `🤔 刚入职就被告知要试用6个月，这合理吗？让我们一起来了解下劳动合同中的试用期那些事儿！

📚 **试用期小知识**：

1️⃣ **时长有上限**：试用期最长不能超过6个月，不是老板想订多久就多久。

2️⃣ **工资有底线**：试用期工资不得低于转正工资的80%，或单位同岗最低工资，两者取其高！

3️⃣ **机会只一次**：同一家公司、同一个岗位，只能约定一次试用期。跳槽回来也不行哦！

💡 **律师提醒**：试用期绝不是"随意解雇期"！公司想在试用期解雇你，也必须证明你不符合录用条件。

✅ **行动建议**：入职时看清合同条款，特别是试用期时长和薪资。有任何疑问，别怕，大胆问！`
        },
        {
          title: "示例二：小红书风格优化",
          input: "第十八条　【夫妻一方的财产】有下列情形之一的，为夫妻一方的财产：\n　　（一）一方的婚前财产；\n　　（二）一方因身体受到伤害获得的医疗费、残疾人生活补助费等费用；\n　　（三）遗嘱或赠与合同中确定只归夫或妻一方的财产；\n　　（四）一方专用的生活用品；\n　　（五）其他应当归一方的财产。",
          output: `婚前财产一定是自己的吗？🤔 离婚时，哪些财产跟你没关系？

关于夫妻个人财产，法律是这么规定的：
💍 婚前财产：结婚前就属于你的，离婚后还是你的！
🤕 意外之财：因身体受伤获得的医疗费、残疾补助金等，属于你个人。
🎁 特别赠与：遗嘱或赠与合同明确只给你的，与对方无关。
🧸 专属用品：你个人专用的生活物品。

律师提醒：搞清楚哪些是你的"私房钱"，才能更好地保护自己的权益！💪`
        },
        {
          title: "示例三：活人感风格优化",
          input: "第十八条　【夫妻一方的财产】有下列情形之一的，为夫妻一方的财产：\n　　（一）一方的婚前财产；\n　　（二）一方因身体受到伤害获得的医疗费、残疾人生活补助费等费用；\n　　（三）遗嘱或赠与合同中确定只归夫或妻一方的财产；\n　　（四）一方专用的生活用品；\n　　（五）其他应当归一方的财产。",
          output: `最近常有朋友问我，婚前财产和婚后财产到底怎么区分？其实民法典里有明确规定。很多时候大家容易忽略的是，婚前财产永远是属于你自己的，不会因为结婚就变成夫妻共同财产。还有一点，如果婚后你因为身体受到意外伤害，获得的医疗费和残疾人生活补助，这些也属于你个人。再比如，遗嘱里明确指定给你的财产，或者你个人专用的生活物品，都属于个人财产。这些规定，其实是为了更好地保护我们每个人的权益，让我们在面对婚姻时，能更安心、更有底气。所以，了解法律，也是保护自己的一种方式。希望我们都能在婚姻里，拥有爱，也拥有保障。❤️`
        },
        {
          title: "示例四：输入具体法律问题",
          input: "房屋买卖中的 \"阳光房\" 问题，如何避免纠纷？",
          output: `🏠 买房送"阳光房"？先别高兴太早，当心这份"美好"背后藏着法律风险！

⚖️ **"阳光房"三大雷区**：

1. **身份不明**：绝大多数阳光房是违章建筑，没有产权登记，随时可能面临被拆除的风险。
2. **面积陷阱**：销售嘴里的"赠送面积"，往往不计入房产证，一旦有纠纷，法律上很难支持。
3. **安全隐患**：搭建质量、防水、承重等问题，都可能成为日后邻里纠纷或安全事故的导火索。

🔍 **购房避坑指南**：

- **核实身份**：买房前，一定要求查看房本和规划图纸，或直接咨询不动产登记中心，确认阳光房的合法性。
- **写入合同**：如果实在喜欢，务必在购房合同中明确约定阳光房的归属、维修责任以及出现问题（如被拆除）的解决方案和赔偿条款。

💡 买房是大事，法律功课要做足。别让"阳光"晃了眼，签合同前多留个心眼！`
        }
      ],
      path: "/optimize"
    },
    generate: {
      title: "内容生成",
      description: "基于法律领域智能生成营销文案",
      icon: FileText,
      color: "from-green-500 to-green-600",
      bgColor: "from-green-50/80 via-emerald-50/60 to-green-100/40",
      examples: [
        {
          title: "示例：基于主题生成营销文案",
          input: "选择领域：劳动用工\n生成主题：工伤赔偿咨询",
          output: "⚠️ 工伤事故发生后，您的权益得到保障了吗？\n\n工伤赔偿包括：\n💰 医疗费用全额报销\n💰 误工费按实际收入计算\n💰 一次性伤残补助金\n💰 护理费、交通费等\n\n📝 专业提醒：\n• 及时申请工伤认定\n• 保留相关证据材料\n• 了解赔偿标准和流程\n\n遇到工伤问题？专业律师为您维权 👇"
        }
      ],
      path: "/content-generate"
    },
    convert: {
      title: "判决书科普",
      description: "将法律文档转换为通俗易懂的内容",
      icon: BookOpen,
      color: "from-purple-500 to-purple-600",
      bgColor: "from-purple-50/80 via-violet-50/60 to-purple-100/40",
      examples: [
        {
          title: "示例：判决书转换为科普内容",
          input: "上传判决书：《张某诉李某合同纠纷案判决书》",
          output: "📋 合同纠纷案例解读\n\n案件要点：\n• 合同一方未按约定履行义务\n• 造成对方经济损失15万元\n• 法院判决违约方承担赔偿责任\n\n💡 法律启示：\n✓ 签合同前仔细审查条款\n✓ 保留履约证据很重要\n✓ 违约后积极协商解决\n\n合同问题找专业律师，避免损失扩大 📞"
        }
      ],
      path: "/document-convert"
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-indigo-50">
      {/* 导航栏 */}
      <nav className="bg-white/80 backdrop-blur-sm border-b border-gray-200/50 sticky top-0 z-50">
        <div className="max-w-7xl mx-auto px-6 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <Button
                variant="ghost"
                onClick={() => navigate('/')}
                className="flex items-center space-x-2 text-gray-600 hover:text-blue-600"
              >
                <ArrowLeft className="h-4 w-4" />
                <span>返回首页</span>
              </Button>
            </div>
            <div className="flex items-center space-x-3">
              <div className="bg-gradient-to-br from-blue-600 to-blue-800 p-2 rounded-xl">
                <Scale className="h-6 w-6 text-white" />
              </div>
              <h1 className="text-xl font-bold text-gray-900">功能演示</h1>
            </div>
            <Button onClick={() => navigate('/app')} className="bg-blue-600 hover:bg-blue-700 text-white px-6 py-2 rounded-xl">
              立即体验
            </Button>
          </div>
        </div>
      </nav>

      {/* 演示内容 */}
      <section className="max-w-7xl mx-auto px-6 py-12">
        <div className="space-y-12">
          {Object.entries(demoData).map(([key, demo]) => {
            const IconComponent = demo.icon;
            
            return (
              <Card
                key={key}
                className={`border-0 shadow-xl bg-gradient-to-br ${demo.bgColor} backdrop-blur-sm rounded-3xl overflow-hidden`}
              >
                <CardHeader className="pb-6">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-4">
                      <div className={`w-16 h-16 bg-gradient-to-br ${demo.color} rounded-2xl flex items-center justify-center shadow-lg`}>
                        <IconComponent className="w-8 h-8 text-white" />
                      </div>
                      <div>
                        <CardTitle className="text-2xl text-gray-900 mb-2">{demo.title}</CardTitle>
                        <p className="text-gray-600">{demo.description}</p>
                      </div>
                    </div>
                    <Button
                      onClick={() => navigate(demo.path)}
                      className={`bg-gradient-to-r ${demo.color} text-white px-6 py-2 rounded-xl hover:scale-105 transition-all duration-200 shadow-lg`}
                    >
                      立即体验
                      <ChevronRight className="w-4 h-4 ml-1" />
                    </Button>
                  </div>
                </CardHeader>

                <CardContent className="space-y-8">
                  {demo.examples.map((example, index) => (
                    <div key={index} className="space-y-4">
                      <h4 className="text-lg font-semibold text-gray-900">{example.title}</h4>
                      <div className="grid md:grid-cols-2 gap-6 items-start">
                        {/* 输入示例 */}
                        <div className="space-y-3">
                          <h5 className="text-md font-medium text-gray-900 flex items-center">
                            <span className="w-6 h-6 bg-red-100 text-red-600 rounded-full flex items-center justify-center text-sm font-bold mr-2">
                              1
                            </span>
                            用户输入
                          </h5>
                          <div className="bg-white/80 backdrop-blur-sm rounded-2xl p-5 border border-gray-200/50 shadow-sm min-h-[120px] flex items-start">
                            <pre className="text-sm text-gray-700 whitespace-pre-wrap font-sans leading-relaxed w-full">
                              {example.input}
                            </pre>
                          </div>
                        </div>

                        {/* 输出示例 */}
                        <div className="space-y-3">
                          <h5 className="text-md font-medium text-gray-900 flex items-center">
                            <span className="w-6 h-6 bg-green-100 text-green-600 rounded-full flex items-center justify-center text-sm font-bold mr-2">
                              2
                            </span>
                            AI优化结果
                          </h5>
                          <div className="bg-white/90 backdrop-blur-sm rounded-2xl p-5 border border-gray-200/50 shadow-md min-h-[120px] max-h-96 overflow-y-auto">
                            <pre className="text-sm text-gray-800 whitespace-pre-wrap font-sans leading-relaxed">
                              {example.output}
                            </pre>
                          </div>
                        </div>
                      </div>
                      {index < demo.examples.length - 1 && (
                        <div className="border-t border-gray-200/50 my-6"></div>
                      )}
                    </div>
                  ))}
                </CardContent>
              </Card>
            );
          })}
        </div>
      </section>
    </div>
  );
};

export default Demo;
