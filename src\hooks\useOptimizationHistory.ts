
import { useState, useEffect } from 'react';
import { supabase } from '@/integrations/supabase/client';
import { useAuth } from '@/contexts/AuthContext';
import { toast } from '@/hooks/use-toast';

export interface OptimizationResult {
  id: string;
  original_text: string;
  optimized_text: string;
  platform: string;
  style: string;
  content_type: string;
  created_at: string;
}

export const useOptimizationHistory = (originalText: string, optimizedText: string, videoScript: string, isOptimizing: boolean) => {
  const [copywritingResults, setCopywritingResults] = useState<OptimizationResult[]>([]);
  const [videoScriptResults, setVideoScriptResults] = useState<OptimizationResult[]>([]);
  const [currentCopywritingIndex, setCurrentCopywritingIndex] = useState(0);
  const [currentVideoScriptIndex, setCurrentVideoScriptIndex] = useState(0);
  const [isLoadingHistory, setIsLoadingHistory] = useState(false);
  const { user } = useAuth();

  // 从数据库获取指定内容类型的优化历史记录
  const fetchOptimizationHistory = async (originalTextToSearch: string, contentType: string, showLoading = false) => {
    if (!user || !originalTextToSearch.trim()) {
      if (contentType === 'copywriting') {
        setCopywritingResults([]);
        setCurrentCopywritingIndex(0);
      } else {
        setVideoScriptResults([]);
        setCurrentVideoScriptIndex(0);
      }
      return;
    }

    if (showLoading) {
      setIsLoadingHistory(true);
    }
    
    try {
      console.log('获取优化历史记录:', { 
        originalText: originalTextToSearch.substring(0, 50) + '...', 
        userId: user.id,
        contentType 
      });
      
      const { data, error } = await supabase
        .from('content_optimization_history')
        .select('*')
        .eq('user_id', user.id)
        .eq('original_text', originalTextToSearch)
        .eq('content_type', contentType)
        .order('created_at', { ascending: false });

      if (error) {
        console.error('获取优化历史记录失败:', error);
        throw error;
      }

      console.log(`${contentType}历史记录获取成功:`, data?.length || 0, '条记录');
      
      const results: OptimizationResult[] = (data || []).map(item => ({
        id: item.id,
        original_text: item.original_text,
        optimized_text: item.optimized_text,
        platform: item.platform || '',
        style: item.style || '',
        content_type: item.content_type || 'copywriting',
        created_at: item.created_at
      }));

      if (contentType === 'copywriting') {
        setCopywritingResults(results);
        setCurrentCopywritingIndex(0);
      } else {
        setVideoScriptResults(results);
        setCurrentVideoScriptIndex(0);
      }
    } catch (error) {
      console.error('获取优化历史记录失败:', error);
      if (contentType === 'copywriting') {
        setCopywritingResults([]);
        setCurrentCopywritingIndex(0);
      } else {
        setVideoScriptResults([]);
        setCurrentVideoScriptIndex(0);
      }
    } finally {
      if (showLoading) {
        setIsLoadingHistory(false);
      }
    }
  };

  // 当原始文案改变时，静默获取两种类型的历史记录
  useEffect(() => {
    if (originalText.trim()) {
      fetchOptimizationHistory(originalText, 'copywriting', false);
      fetchOptimizationHistory(originalText, 'video-script', false);
    } else {
      setCopywritingResults([]);
      setVideoScriptResults([]);
      setCurrentCopywritingIndex(0);
      setCurrentVideoScriptIndex(0);
    }
  }, [originalText, user]);

  // 当有新的优化结果时，重新获取历史记录以保持同步
  useEffect(() => {
    if (optimizedText && !isOptimizing && originalText.trim()) {
      setTimeout(() => {
        fetchOptimizationHistory(originalText, 'copywriting', false);
      }, 1000);
    }
  }, [optimizedText, isOptimizing, originalText]);

  useEffect(() => {
    if (videoScript && !isOptimizing && originalText.trim()) {
      setTimeout(() => {
        fetchOptimizationHistory(originalText, 'video-script', false);
      }, 1000);
    }
  }, [videoScript, isOptimizing, originalText]);

  const handlePreviousCopywriting = () => {
    if (currentCopywritingIndex > 0) {
      setCurrentCopywritingIndex(currentCopywritingIndex - 1);
    }
  };

  const handleNextCopywriting = () => {
    if (currentCopywritingIndex < copywritingResults.length - 1) {
      setCurrentCopywritingIndex(currentCopywritingIndex + 1);
    }
  };

  const handlePreviousVideoScript = () => {
    if (currentVideoScriptIndex > 0) {
      setCurrentVideoScriptIndex(currentVideoScriptIndex - 1);
    }
  };

  const handleNextVideoScript = () => {
    if (currentVideoScriptIndex < videoScriptResults.length - 1) {
      setCurrentVideoScriptIndex(currentVideoScriptIndex + 1);
    }
  };

  // 获取当前显示的文案内容
  const getCurrentCopywritingText = () => {
    if (isOptimizing) {
      return optimizedText;
    }
    
    if (copywritingResults.length > 0 && copywritingResults[currentCopywritingIndex]) {
      return copywritingResults[currentCopywritingIndex].optimized_text;
    }
    
    return optimizedText;
  };

  // 获取当前显示的短视频脚本内容
  const getCurrentVideoScriptText = () => {
    if (isOptimizing) {
      return videoScript;
    }
    
    if (videoScriptResults.length > 0 && videoScriptResults[currentVideoScriptIndex]) {
      return videoScriptResults[currentVideoScriptIndex].optimized_text;
    }
    
    return videoScript;
  };

  const getCurrentCopywritingResult = () => {
    return copywritingResults[currentCopywritingIndex];
  };

  const getCurrentVideoScriptResult = () => {
    return videoScriptResults[currentVideoScriptIndex];
  };

  return {
    copywritingResults,
    videoScriptResults,
    currentCopywritingIndex,
    currentVideoScriptIndex,
    isLoadingHistory,
    handlePreviousCopywriting,
    handleNextCopywriting,
    handlePreviousVideoScript,
    handleNextVideoScript,
    getCurrentCopywritingText,
    getCurrentVideoScriptText,
    getCurrentCopywritingResult,
    getCurrentVideoScriptResult,
  };
};
