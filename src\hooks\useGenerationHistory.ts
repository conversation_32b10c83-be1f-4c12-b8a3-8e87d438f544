
import { useState, useEffect, useCallback } from 'react';
import { supabase } from '@/integrations/supabase/client';
import { toast } from '@/hooks/use-toast';
import { useAuth } from '@/contexts/AuthContext';
import { saveGenerationHistory } from '@/services/historyService';

export interface GenerationHistoryItem {
  id: string;
  domain: string;
  topic: string | null;
  generated_content: string;
  created_at: string;
}

export const useGenerationHistory = () => {
  const [generationHistory, setGenerationHistory] = useState<GenerationHistoryItem[]>([]);
  const [isLoadingHistory, setIsLoadingHistory] = useState(false);
  const { user } = useAuth();

  const fetchGenerationHistory = useCallback(async () => {
    if (!user) {
      console.log('用户未登录，无法获取生成历史记录');
      return;
    }
    
    setIsLoadingHistory(true);
    try {
      console.log('开始获取生成历史记录，用户ID:', user.id);
      
      const { data, error } = await supabase
        .from('content_generation_history')
        .select('*')
        .eq('user_id', user.id)
        .order('created_at', { ascending: false })
        .limit(50);

      if (error) {
        console.error('获取生成历史记录失败:', error);
        throw error;
      }

      console.log('生成历史记录获取成功:', data?.length || 0, '条记录');
      setGenerationHistory(data || []);
    } catch (error) {
      console.error('获取生成历史记录失败:', error);
      toast({
        title: "获取历史记录失败",
        description: error instanceof Error ? error.message : "未知错误",
        variant: "destructive",
      });
    } finally {
      setIsLoadingHistory(false);
    }
  }, [user]);

  const saveHistory = useCallback(async (
    domain: string,
    topic: string | null,
    generatedContent: string
  ) => {
    if (!user) {
      console.log('用户未登录，无法保存生成历史记录');
      return;
    }

    try {
      console.log('保存生成历史记录:', { domain, topic, contentLength: generatedContent.length });
      await saveGenerationHistory(user.id, domain, topic, generatedContent);
      console.log('生成历史记录保存成功');
      
      // 保存后刷新历史记录
      fetchGenerationHistory();
    } catch (error) {
      console.error('保存生成历史记录失败:', error);
    }
  }, [user, fetchGenerationHistory]);

  const deleteHistoryItem = useCallback(async (id: string) => {
    try {
      console.log('删除生成历史记录:', id);
      
      const { error } = await supabase
        .from('content_generation_history')
        .delete()
        .eq('id', id);

      if (error) {
        console.error('删除生成历史记录失败:', error);
        throw error;
      }
      
      setGenerationHistory(prev => prev.filter(item => item.id !== id));
      console.log('生成历史记录删除成功');
      toast({
        title: "删除成功",
        description: "历史记录已删除",
      });
    } catch (error) {
      console.error('删除生成历史记录失败:', error);
      toast({
        title: "删除失败",
        description: error instanceof Error ? error.message : "未知错误",
        variant: "destructive",
      });
    }
  }, []);

  const copyContent = useCallback((content: string) => {
    navigator.clipboard.writeText(content);
    toast({
      title: "复制成功",
      description: "生成内容已复制到剪贴板",
    });
  }, []);

  return {
    generationHistory,
    isLoadingHistory,
    fetchGenerationHistory,
    saveHistory,
    deleteHistoryItem,
    copyContent,
  };
};
