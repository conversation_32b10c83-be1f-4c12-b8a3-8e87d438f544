// 最简化的测试入口点
exports.handler = async (event, context) => {
  console.log('接收到事件:', JSON.stringify(event, null, 2));
  console.log('上下文:', JSON.stringify(context, null, 2));
  
  return {
    statusCode: 200,
    headers: {
      'Content-Type': 'application/json',
      'Access-Control-Allow-Origin': '*',
    },
    body: JSON.stringify({ 
      message: '测试成功！',
      timestamp: new Date().toISOString(),
      event: event
    })
  };
};