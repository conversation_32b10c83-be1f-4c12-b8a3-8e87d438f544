
import { serve } from "https://deno.land/std@0.168.0/http/server.ts"
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'
import { GeminiService } from './geminiService.ts'
import { DeepseekService } from './deepseekService.ts'
import { DoubaoService } from './doubaoService.ts'
import { cleanXiaohongshuContent } from './textProcessing.ts'

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

serve(async (req) => {
  if (req.method === 'OPTIONS') {
    return new Response(null, { headers: corsHeaders })
  }

  try {
    const supabase = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? ''
    )

    // 验证用户身份
    const authHeader = req.headers.get('Authorization')
    if (!authHeader) {
      throw new Error('No authorization header')
    }

    const { data: { user }, error: userError } = await supabase.auth.getUser(
      authHeader.replace('Bearer ', '')
    )

    if (userError || !user) {
      throw new Error('Invalid user')
    }

    const { action, data } = await req.json()
    
    // 获取AI模型配置
    const { data: modelConfig } = await supabase
      .from('ai_model_config')
      .select('config_value')
      .eq('config_key', 'preferred_model')
      .single()
    
    const preferredModel = modelConfig?.config_value || 'doubao'
    console.log(`Processing request: ${action} for user: ${user.id} using model: ${preferredModel}`)

    let result
    
    // 临时回退机制：如果豆包失败，使用Gemini
    try {
      if (preferredModel === 'doubao') {
        const doubaoService = new DoubaoService()
        
        switch (action) {
          case 'generateTopics':
            result = await doubaoService.generateTopics(data.domain)
            break
          case 'optimize':
            result = await doubaoService.optimizeContent(data.text, data.platform, data.style)
            break
          case 'generateVideoScript':
            result = await doubaoService.generateVideoScript(data.text, data.platform, data.style)
            break
          case 'generateContent':
            result = await doubaoService.generateContentByDomain(data.domain)
            break
          case 'generateContentByTopic':
            result = await doubaoService.generateContentByTopic(data.topic)
            break
          case 'generateContentByTopicStream':
            // 对于流式生成，暂时使用非流式方式返回结果
            const contents = await doubaoService.generateContentByTopic(data.topic)
            // 返回第一条内容作为单条文案，并进行清理
            const rawContent = contents.length > 0 ? contents[0] : ''
            result = cleanXiaohongshuContent(rawContent)
            break
          case 'popularize':
            result = await doubaoService.popularizeContent(data.content)
            break
          case 'extractDocumentSummary':
            result = await doubaoService.extractDocumentSummary(data.content)
            break
          default:
            throw new Error('Invalid action')
        }
      } else {
        // 其他模型逻辑保持不变
        throw new Error('Using fallback to other models')
      }
    } catch (doubaoError) {
      console.error('Doubao service failed, falling back to Gemini:', doubaoError)
      
      // 回退到Gemini
      const geminiService = new GeminiService()
      
      switch (action) {
        case 'generateTopics':
          result = await geminiService.generateTopics(data.domain)
          break
        case 'optimize':
          result = await geminiService.optimizeContent(data.text, data.platform, data.style)
          break
        case 'generateVideoScript':
          result = await geminiService.generateVideoScript(data.text, data.platform, data.style)
          break
        case 'generateContent':
          result = await geminiService.generateContentByDomain(data.domain)
          break
        case 'generateContentByTopic':
          result = await geminiService.generateContentByTopic(data.topic)
          break
        case 'generateContentByTopicStream':
          const contents = await geminiService.generateContentByTopic(data.topic)
          const rawContent = contents.length > 0 ? contents[0] : ''
          result = cleanXiaohongshuContent(rawContent)
          break
        case 'popularize':
          result = await geminiService.popularizeContent(data.content)
          break
        case 'extractDocumentSummary':
          result = await geminiService.extractDocumentSummary(data.content)
          break
        default:
          throw new Error('Invalid action')
      }
    }
    
    if (preferredModel === 'deepseek') {
      const deepseekService = new DeepseekService()
      
      switch (action) {
        case 'generateTopics':
          result = await deepseekService.generateTopics(data.domain)
          break
        case 'optimize':
          result = await deepseekService.optimizeContent(data.text, data.platform, data.style)
          break
        case 'generateVideoScript':
          // Deepseek 暂不支持短视频脚本生成，回退到 Gemini
          const geminiServiceForScript = new GeminiService()
          result = await geminiServiceForScript.generateVideoScript(data.text, data.platform, data.style)
          break
        case 'generateContent':
          result = await deepseekService.generateContentByDomain(data.domain)
          break
        case 'generateContentByTopic':
          result = await deepseekService.generateContentByTopic(data.topic)
          break
        case 'generateContentByTopicStream':
          // 对于流式生成，暂时使用非流式方式返回结果
          const contents = await deepseekService.generateContentByTopic(data.topic)
          // 返回第一条内容作为单条文案，并进行清理
          const rawContent = contents.length > 0 ? contents[0] : ''
          result = cleanXiaohongshuContent(rawContent)
          break
        case 'popularize':
          result = await deepseekService.popularizeContent(data.content)
          break
        case 'extractDocumentSummary':
          // 使用Gemini进行文档摘要提取，因为这是专门的功能
          const geminiService = new GeminiService()
          result = await geminiService.extractDocumentSummary(data.content)
          break
        default:
          throw new Error('Invalid action')
      }
    } else {
      // 使用Gemini
      const geminiService = new GeminiService()
      
      switch (action) {
        case 'generateTopics':
          result = await geminiService.generateTopics(data.domain)
          break
        case 'optimize':
          result = await geminiService.optimizeContent(data.text, data.platform, data.style)
          break
        case 'generateVideoScript':
          result = await geminiService.generateVideoScript(data.text, data.platform, data.style)
          break
        case 'generateContent':
          result = await geminiService.generateContentByDomain(data.domain)
          break
        case 'generateContentByTopic':
          result = await geminiService.generateContentByTopic(data.topic)
          break
        case 'generateContentByTopicStream':
          // 对于流式生成，暂时使用非流式方式返回单条结果
          const contents = await geminiService.generateContentByTopic(data.topic)
          // 返回第一条内容作为单条文案，并进行清理
          const rawContent = contents.length > 0 ? contents[0] : ''
          result = cleanXiaohongshuContent(rawContent)
          console.log(`Raw content for topic: ${rawContent.substring(0, 100)}...`)
          console.log(`Cleaned content: ${result.substring(0, 100)}...`)
          break
        case 'popularize':
          result = await geminiService.popularizeContent(data.content)
          break
        case 'extractDocumentSummary':
          result = await geminiService.extractDocumentSummary(data.content)
          break
        default:
          throw new Error('Invalid action')
      }
    }

    return new Response(
      JSON.stringify({ result }),
      { headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
    )

  } catch (error) {
    console.error('Error in gemini-ai function:', error)
    return new Response(
      JSON.stringify({ error: error.message }),
      { 
        status: 500,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
      }
    )
  }
})
