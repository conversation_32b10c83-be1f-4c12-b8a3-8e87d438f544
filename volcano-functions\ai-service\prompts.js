// 从现有代码移植的提示词模板

function generateTopicsPrompt(domain) {
  return `请为"${domain}"这个领域生成10个适合做内容创作的热门选题。

要求：
1. 选题要具有时效性和热度
2. 适合在社交媒体平台传播
3. 能引起目标受众的共鸣和讨论
4. 每个选题控制在15字以内
5. 选题要有差异化，避免重复

请按照以下格式输出：
1. 选题一
2. 选题二
3. 选题三
...

注意：只输出选题列表，不要额外的解释说明。`;
}

function optimizeContentPrompt(text, platform = "", style = "") {
  const platformSpecific = platform ? `\n\n针对${platform}平台的特点进行优化。` : '';
  const styleSpecific = style ? `\n\n采用${style}的风格特色。` : '';
  
  return `请将以下内容优化成更吸引人的文案：

${text}

优化要求：
1. 保持原意不变
2. 增强可读性和吸引力
3. 适当使用表情符号和话题标签
4. 语言要生动有趣，贴近用户
5. 结构清晰，重点突出${platformSpecific}${styleSpecific}

请直接输出优化后的文案，不要额外的解释说明。`;
}

function generateContentPrompt(domain) {
  return `请为"${domain}"领域创作3-5条优质内容文案。

内容要求：
1. 每条文案100-300字
2. 适合社交媒体传播
3. 有吸引力的标题或开头
4. 包含实用价值或情感共鸣
5. 适当使用表情符号和话题标签

请用分隔线(---)分开每条文案，直接输出内容，不要额外说明。`;
}

function generateContentByTopicPrompt(topic) {
  return `请围绕"${topic}"这个主题，创作一篇适合小红书平台的文案。

要求：
1. 字数150-300字
2. 开头要有吸引力，能够引发用户好奇
3. 内容要有实用价值或情感共鸣
4. 适当使用表情符号增加趣味性
5. 结尾要有互动引导（如：你们觉得呢？欢迎评论区分享等）
6. 包含2-3个相关话题标签
7. 语言风格要轻松活泼，贴近年轻用户

请直接输出文案内容，不要添加任何解释说明或标题。`;
}

function popularizeContentPrompt(content) {
  return `请将以下专业内容进行科普化改写，让普通读者也能轻松理解：

${content}

改写要求：
1. 用通俗易懂的语言替换专业术语
2. 适当增加例子和比喻来说明抽象概念
3. 保持内容的准确性和完整性
4. 结构清晰，逻辑清楚
5. 语言生动有趣，增加可读性

请直接输出科普化后的内容，不要额外的解释说明。`;
}

function extractDocumentSummaryPrompt(content) {
  return `请从以下法律文书中提取争议焦点与裁判要点：

${content}

请按照以下格式整理争议焦点与裁判要点：

每个争议焦点请按以下格式：
序号. [争议类型] 争议描述 [裁判要点] 法院的认定和理由

要求：
1. 争议类型可以是：事实争议、法律争议、程序争议等
2. 争议描述要简洁明确，突出争议的核心问题
3. 裁判要点要包含法院的具体认定和法律依据
4. 如果文档内容不足，请如实说明"未能从文书中提炼出完整的争议焦点与裁判要点"
5. 最多提取10个最重要的争议焦点

请严格按照要求的格式输出，不要添加其他解释说明。`;
}

function generateVideoScriptPrompt(text, platform = "", style = "") {
  const platformSpecific = platform ? `适合${platform}平台的特点` : '适合短视频平台';
  const styleSpecific = style ? `采用${style}风格` : '';
  
  return `请基于以下内容创作一个短视频脚本：

${text}

脚本要求：
1. 时长30-60秒，适合短视频格式
2. ${platformSpecific}
3. 开头3秒要有强烈吸引力
4. 结构清晰：开头吸引-核心内容-结尾互动
5. 语言口语化，适合配音朗读
6. 包含视觉画面提示
7. 有明确的行动召唤${styleSpecific ? `\n8. ${styleSpecific}` : ''}

请按以下格式输出：
【开头】(0-3秒)
画面：[画面描述]
文案：[配音文案]

【主体】(3-50秒)  
画面：[画面描述]
文案：[配音文案]

【结尾】(50-60秒)
画面：[画面描述]
文案：[配音文案]

直接输出脚本内容，不要额外说明。`;
}

module.exports = {
  generateTopicsPrompt,
  optimizeContentPrompt,
  generateContentPrompt,
  generateContentByTopicPrompt,
  popularizeContentPrompt,
  extractDocumentSummaryPrompt,
  generateVideoScriptPrompt
};