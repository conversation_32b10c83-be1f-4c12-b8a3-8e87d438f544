# 豆包AI服务部署说明

## 环境变量配置

在火山引擎云函数控制台中，需要配置以下环境变量：

### 必需的环境变量

```bash
DOUBAO_API_KEY=df725996-30e3-4769-84f1-8c3737dccc58
```

## 模型配置

当前使用的豆包模型：
- **模型名称**: Doubao-Seed-1.6-thinking
- **Model ID**: `doubao-seed-1-6-thinking-250615`
- **API端点**: `https://ark.cn-beijing.volces.com/api/v3/chat/completions`

## 功能验证

### 1. 生成选题功能
- ✅ 已测试通过
- 能够根据指定领域生成10个相关选题
- 支持多种内容领域（法律、科技、健康等）

### 2. 内容优化功能
- ✅ 已测试通过
- 支持多种平台风格（小红书、抖音、微博等）
- 能够保持原意的同时增强吸引力

## 部署步骤

1. 确保在火山引擎云函数控制台中配置了正确的环境变量
2. 上传代码到云函数
3. 测试API端点是否正常工作
4. 验证与前端应用的集成

## 注意事项

- API密钥已在代码中设置默认值，但建议在生产环境中使用环境变量
- 模型ID必须使用完整的ID：`doubao-seed-1-6-thinking-250615`
- 确保火山引擎账户有足够的API调用额度

## 故障排除

如果遇到404错误（模型不存在），请检查：
1. Model ID是否正确
2. API密钥是否有效
3. 是否有访问该模型的权限
