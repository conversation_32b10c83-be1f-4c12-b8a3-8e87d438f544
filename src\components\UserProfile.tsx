
import React from 'react';
import { Button } from '@/components/ui/button';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { LogOut, User, Mail, Shield } from 'lucide-react';
import { useAuth } from '@/contexts/AuthContext';
import { toast } from '@/hooks/use-toast';
import { useNavigate } from 'react-router-dom';

const UserProfile = () => {
  const { user, signOut } = useAuth();
  const navigate = useNavigate();

  const handleSignOut = async () => {
    try {
      await signOut();
      toast({
        title: '已退出登录',
        description: '您已成功退出登录',
      });
      navigate('/', { replace: true });
    } catch (error) {
      console.error('退出登录错误:', error);
      if (error instanceof Error && (error.message.includes('Session not found') || error.message.includes('session_not_found'))) {
        toast({
          title: '已退出登录',
          description: '您已成功退出登录',
        });
        navigate('/', { replace: true });
      } else {
        toast({
          title: '退出失败',
          description: '请稍后重试',
          variant: 'destructive',
        });
      }
    }
  };

  if (!user) return null;

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button 
          variant="ghost" 
          className="flex items-center space-x-3 px-4 py-2 rounded-xl bg-gray-50/80 hover:bg-white hover:shadow-sm transition-all duration-200 focus:outline-none focus:ring-0 focus-visible:ring-0 focus-visible:ring-offset-0"
        >
          <Avatar className="h-8 w-8">
            <AvatarImage src={user.user_metadata?.avatar_url} />
            <AvatarFallback className="text-xs">
              <User className="h-4 w-4" />
            </AvatarFallback>
          </Avatar>
          <div className="hidden md:block text-left">
            <p className="text-sm font-medium text-gray-900">
              {user.user_metadata?.full_name || user.email}
            </p>
            <p className="text-xs text-gray-500">{user.email}</p>
          </div>
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent className="w-80 bg-white/95 backdrop-blur-lg border border-gray-200/50 shadow-2xl rounded-2xl" align="end">
        {/* 用户信息头部 */}
        <div className="p-6 bg-gradient-to-br from-blue-50/80 via-indigo-50/60 to-blue-100/40 rounded-t-2xl border-b border-gray-100">
          <div className="flex items-center space-x-4">
            <div className="relative">
              <Avatar className="h-12 w-12 ring-2 ring-white shadow-lg">
                <AvatarImage src={user.user_metadata?.avatar_url} />
                <AvatarFallback className="bg-gradient-to-br from-blue-500 to-blue-600 text-white">
                  <User className="h-6 w-6" />
                </AvatarFallback>
              </Avatar>
              <div className="absolute -bottom-1 -right-1 w-4 h-4 bg-green-500 rounded-full border-2 border-white"></div>
            </div>
            <div className="flex-1 min-w-0">
              <h3 className="text-lg font-semibold text-gray-900 truncate">
                {user.user_metadata?.full_name || '用户'}
              </h3>
              <div className="flex items-center space-x-1 mt-1">
                <Mail className="h-3 w-3 text-gray-500" />
                <p className="text-sm text-gray-600 truncate">{user.email}</p>
              </div>
              <div className="flex items-center space-x-1 mt-1">
                <Shield className="h-3 w-3 text-green-500" />
                <span className="text-xs text-green-600 font-medium">已验证账户</span>
              </div>
            </div>
          </div>
        </div>

        {/* 账户状态信息 */}
        <div className="p-4">
          <div className="bg-gradient-to-r from-blue-50/50 to-indigo-50/50 rounded-xl p-4 border border-blue-100/50">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-700">账户状态</p>
                <p className="text-xs text-gray-500 mt-1">活跃用户 · 今日已使用</p>
              </div>
              <div className="w-8 h-8 bg-gradient-to-br from-green-400 to-green-500 rounded-full flex items-center justify-center">
                <Shield className="w-4 h-4 text-white" />
              </div>
            </div>
          </div>
        </div>

        <DropdownMenuSeparator className="my-2 mx-4" />

        {/* 退出登录按钮 */}
        <div className="p-4">
          <DropdownMenuItem 
            onClick={handleSignOut} 
            className="w-full p-3 rounded-xl text-red-600 hover:text-red-700 hover:bg-red-50/80 focus:bg-red-50/80 focus:text-red-700 transition-all duration-200 cursor-pointer group"
          >
            <div className="flex items-center space-x-3 w-full">
              <div className="w-8 h-8 bg-red-100 group-hover:bg-red-200 rounded-lg flex items-center justify-center transition-colors duration-200">
                <LogOut className="w-4 h-4 text-red-600" />
              </div>
              <div className="flex-1">
                <span className="font-medium">退出登录</span>
                <p className="text-xs text-gray-500 mt-0.5">安全退出当前账户</p>
              </div>
            </div>
          </DropdownMenuItem>
        </div>
      </DropdownMenuContent>
    </DropdownMenu>
  );
};

export default UserProfile;
