
interface LoadingSpinnerProps {
  title: string;
  description: string;
  color?: string;
}

const LoadingSpinner = ({ title, description, color = "blue" }: LoadingSpinnerProps) => {
  const colorClasses = {
    blue: "border-blue-500/30 border-t-blue-500",
    purple: "border-purple-500/30 border-t-purple-500"
  };

  return (
    <div className="flex flex-col items-center justify-center py-20">
      <div className={`w-20 h-20 border-4 ${colorClasses[color as keyof typeof colorClasses]} rounded-full animate-spin mb-8 shadow-lg`}></div>
      <div className="bg-white/80 backdrop-blur-md rounded-3xl p-8 max-w-md mx-auto shadow-xl border border-white/50">
        <h3 className="text-xl font-semibold text-gray-900 mb-3 text-center">{title}</h3>
        <p className="text-gray-600 text-center">{description}</p>
      </div>
    </div>
  );
};

export default LoadingSpinner;
