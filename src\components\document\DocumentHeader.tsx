import React from 'react';
import { useNavigate } from 'react-router-dom';
import { ArrowLeft, FileText, History } from 'lucide-react';
import { Button } from '@/components/ui/button';
interface DocumentHeaderProps {
  onToggleHistory: () => void;
}
export const DocumentHeader: React.FC<DocumentHeaderProps> = ({
  onToggleHistory
}) => {
  const navigate = useNavigate();
  return <div className="bg-white/80 backdrop-blur-sm border-b border-gray-200/50 shadow-sm">
      <div className="max-w-6xl mx-auto px-6 py-4">
        <div className="flex items-center justify-between">
          <Button variant="ghost" onClick={() => navigate('/app')} className="flex items-center space-x-2 text-gray-700 hover:text-blue-600 hover:bg-blue-50/50">
            <ArrowLeft className="w-4 h-4" />
            <span>返回内容优化与生成</span>
          </Button>
          
          <div className="flex items-center space-x-3">
            <div className="bg-gradient-to-br from-blue-500 to-blue-700 p-2 rounded-xl shadow-sm">
              <FileText className="h-5 w-5 text-white" />
            </div>
            <div>
              <h1 className="text-xl font-bold text-gray-900">文档科普转换</h1>
              <p className="text-sm text-gray-600">AI驱动的专业文档转换工具</p>
            </div>
          </div>
          
          <Button variant="outline" onClick={onToggleHistory} className="flex items-center space-x-2 bg-gradient-to-r from-blue-50 to-blue-100 border-2 border-blue-200 hover:border-blue-300 text-blue-700 hover:text-blue-800 rounded-xl px-4 py-2 transition-all duration-300 hover:shadow-lg hover:shadow-blue-200/50">
            <History className="w-4 h-4" />
            <span>历史记录</span>
          </Button>
        </div>
      </div>
    </div>;
};