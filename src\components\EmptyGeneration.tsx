
import { But<PERSON> } from "@/components/ui/button";
import { Sparkles } from "lucide-react";

interface EmptyGenerationProps {
  onGenerate: () => void;
}

const EmptyGeneration = ({ onGenerate }: EmptyGenerationProps) => {
  return (
    <div className="text-center py-20">
      <div className="bg-white rounded-lg p-8 shadow-lg max-w-md mx-auto">
        <Sparkles className="w-12 h-12 text-gray-400 mx-auto mb-4" />
        <h3 className="text-xl font-semibold text-gray-900 mb-2">暂无内容</h3>
        <p className="text-gray-600 mb-4">点击"重新生成"开始创建内容</p>
        <Button onClick={onGenerate} className="bg-blue-600 hover:bg-blue-700 text-white">
          开始生成
        </Button>
      </div>
    </div>
  );
};

export default EmptyGeneration;
