import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { testConcurrentProcessing, compareProcessingMethods, generateTestTopics } from '@/utils/concurrentProcessingTest';

const TestConcurrent = () => {
  const [isRunning, setIsRunning] = useState(false);
  const [results, setResults] = useState<any>(null);
  const [logs, setLogs] = useState<string[]>([]);

  const addLog = (message: string) => {
    setLogs(prev => [...prev, `${new Date().toLocaleTimeString()}: ${message}`]);
  };

  const runConcurrentTest = async () => {
    setIsRunning(true);
    setResults(null);
    setLogs([]);
    
    try {
      addLog('开始并发处理测试...');
      const testTopics = generateTestTopics(10);
      addLog(`生成了${testTopics.length}个测试选题`);
      
      const result = await testConcurrentProcessing(testTopics);
      setResults(result);
      addLog('测试完成！');
    } catch (error) {
      addLog(`测试失败: ${error instanceof Error ? error.message : '未知错误'}`);
    } finally {
      setIsRunning(false);
    }
  };

  const runComparisonTest = async () => {
    setIsRunning(true);
    setResults(null);
    setLogs([]);
    
    try {
      addLog('开始性能对比测试...');
      const testTopics = generateTestTopics(5); // 使用较少的选题进行对比测试
      addLog(`生成了${testTopics.length}个测试选题`);
      
      const result = await compareProcessingMethods(testTopics);
      setResults(result);
      addLog('对比测试完成！');
    } catch (error) {
      addLog(`测试失败: ${error instanceof Error ? error.message : '未知错误'}`);
    } finally {
      setIsRunning(false);
    }
  };

  return (
    <div className="container mx-auto p-6 space-y-6">
      <Card>
        <CardHeader>
          <CardTitle>并发处理测试工具</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex space-x-4">
            <Button 
              onClick={runConcurrentTest}
              disabled={isRunning}
              className="flex-1"
            >
              {isRunning ? '测试中...' : '测试并发处理'}
            </Button>
            <Button 
              onClick={runComparisonTest}
              disabled={isRunning}
              variant="outline"
              className="flex-1"
            >
              {isRunning ? '测试中...' : '性能对比测试'}
            </Button>
          </div>
          
          {logs.length > 0 && (
            <Card>
              <CardHeader>
                <CardTitle className="text-sm">测试日志</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="bg-gray-100 p-4 rounded-lg max-h-40 overflow-y-auto">
                  {logs.map((log, index) => (
                    <div key={index} className="text-xs font-mono text-gray-700">
                      {log}
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          )}
          
          {results && (
            <Card>
              <CardHeader>
                <CardTitle className="text-sm">测试结果</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  {results.totalTime && (
                    <>
                      <div className="flex justify-between">
                        <span>总耗时:</span>
                        <span className="font-mono">{(results.totalTime / 1000).toFixed(2)}秒</span>
                      </div>
                      <div className="flex justify-between">
                        <span>成功数量:</span>
                        <span className="font-mono text-green-600">{results.successCount}</span>
                      </div>
                      <div className="flex justify-between">
                        <span>失败数量:</span>
                        <span className="font-mono text-red-600">{results.failureCount}</span>
                      </div>
                      <div className="flex justify-between">
                        <span>成功率:</span>
                        <span className="font-mono">{Math.round(results.successRate * 100)}%</span>
                      </div>
                    </>
                  )}
                  
                  {results.speedup && (
                    <>
                      <hr className="my-4" />
                      <div className="text-sm font-semibold text-blue-600">性能对比结果:</div>
                      <div className="flex justify-between">
                        <span>速度提升:</span>
                        <span className="font-mono text-green-600">{results.speedup.toFixed(2)}x</span>
                      </div>
                      <div className="flex justify-between">
                        <span>顺序处理耗时:</span>
                        <span className="font-mono">{(results.sequential.time / 1000).toFixed(2)}秒</span>
                      </div>
                      <div className="flex justify-between">
                        <span>并发处理耗时:</span>
                        <span className="font-mono">{(results.concurrent.totalTime / 1000).toFixed(2)}秒</span>
                      </div>
                    </>
                  )}
                </div>
              </CardContent>
            </Card>
          )}
        </CardContent>
      </Card>
    </div>
  );
};

export default TestConcurrent;
