
import { removeMarkdownFormatting } from './markdownCleaning.ts';

// Improved AI generated content cleaning function - maintains paragraph structure
export function cleanAIGeneratedContent(text: string): string {
  let cleanedText = removeMarkdownFormatting(text);
  
  // Remove AI introductory phrases at the beginning
  const introPatterns = [
    /^(OK，收到！|好的！|收到！|明白了！)*[，。！]*\s*/,
    /^下面是.*?[：:]\s*/,
    /^根据.*?要求.*?[：:]\s*/,
    /^针对.*?问题.*?[：:]\s*/,
    /^为您.*?生成.*?[：:]\s*/,
    /^以下是.*?[：:]\s*/,
    /^这里是.*?[：:]\s*/,
    /^我来.*?[：:]\s*/,
    /^现在.*?[：:]\s*/
  ];
  
  // Apply intro pattern removal
  for (const pattern of introPatterns) {
    cleanedText = cleanedText.replace(pattern, '');
  }
  
  return cleanedText
    // Remove special symbols and markers
    .replace(/[【】\[\]]/g, '')
    .replace(/[《》<>]/g, '')
    // Remove separators and decorative symbols
    .replace(/[-=]{3,}/g, '')
    .replace(/\*{2,}/g, '')
    .replace(/#{2,}/g, '')
    // Maintain paragraph structure cleaning
    .replace(/\n{4,}/g, '\n\n\n')  // Max 3 line breaks
    .replace(/\n{2}/g, '||PARAGRAPH||')  // Temporary paragraph marker
    .replace(/[ \t]{2,}/g, ' ')  // Clean inline extra spaces
    .replace(/\|\|PARAGRAPH\|\|/g, '\n\n')  // Restore paragraph separation
    .trim();
}
