import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Co<PERSON>, ArrowLeft } from "lucide-react";
import { toast } from "@/hooks/use-toast";
interface ContentListProps {
  generatedContent: string[];
  selectedTopic: string;
  currentDomainInfo: {
    tag: string;
  };
  onBackToTopics: () => void;
}
const ContentList = ({
  generatedContent,
  selectedTopic,
  currentDomainInfo,
  onBackToTopics
}: ContentListProps) => {
  const copyContent = (content: string, index: number) => {
    navigator.clipboard.writeText(content);
    toast({
      title: "复制成功",
      description: `第${index + 1}条文案已复制到剪贴板`
    });
  };
  return <div className="space-y-8">
      <div className="text-center">
        <div className="bg-white/70 backdrop-blur-md rounded-3xl p-8 max-w-4xl mx-auto shadow-xl border border-white/50 mb-8">
          <h2 className="text-3xl font-bold text-gray-900 mb-4">
            📝 生成的文案内容
          </h2>
          <p className="text-lg text-gray-600 mb-2">
            选题：{selectedTopic}
          </p>
          <p className="text-sm text-gray-500 mb-6">以下是AI为该选题生成专业法律文案</p>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
        {generatedContent.map((content, index) => <Card key={index} className="shadow-xl border-0 bg-white/80 backdrop-blur-md hover:shadow-2xl transition-all duration-300 h-80 flex flex-col rounded-3xl overflow-hidden hover:scale-105 hover:-translate-y-1">
            <CardContent className="p-6 flex flex-col h-full">
              <div className="flex justify-between items-start mb-4 flex-shrink-0">
                <div className="flex items-center space-x-2 flex-wrap">
                  <div className="bg-gradient-to-br from-purple-400 to-purple-600 text-white px-3 py-1.5 rounded-full text-xs font-medium shadow-lg">
                    #{index + 1}
                  </div>
                  <div className="bg-gradient-to-br from-blue-400 to-blue-600 text-white px-3 py-1.5 rounded-full text-xs font-medium shadow-lg">
                    #{currentDomainInfo.tag}
                  </div>
                  <div className="bg-gradient-to-br from-green-400 to-green-600 text-white px-3 py-1.5 rounded-full text-xs font-medium shadow-lg">
                    #小红书文案
                  </div>
                </div>
                <Button onClick={() => copyContent(content, index)} variant="outline" size="sm" className="flex items-center space-x-1.5 hover:bg-blue-50/80 hover:border-blue-300 text-sm px-4 py-2 h-auto flex-shrink-0 rounded-full backdrop-blur-sm border-gray-200/70 transition-all duration-200 shadow-sm hover:shadow-md">
                  <Copy className="w-3.5 h-3.5" />
                  <span>复制</span>
                </Button>
              </div>
              
              <div className="text-gray-800 flex-1 overflow-y-auto scrollbar-thin scrollbar-thumb-gray-300 scrollbar-track-gray-100">
                {content.split('\n').map((paragraph, idx) => {
              if (paragraph.trim()) {
                return <p key={idx} className="mb-2 last:mb-0 leading-relaxed text-sm">
                        {paragraph.trim()}
                      </p>;
              }
              return null;
            }).filter(Boolean)}
              </div>
            </CardContent>
          </Card>)}
      </div>

      <div className="flex flex-col items-center space-y-4 pt-8">
        <Button onClick={onBackToTopics} variant="outline" size="lg" className="px-8 py-3 text-lg rounded-full shadow-lg hover:shadow-xl transition-all duration-300 backdrop-blur-sm border-gray-200/70 hover:border-blue-300 hover:bg-blue-50/50">
          <ArrowLeft className="w-5 h-5 mr-2" />
          返回选题列表
        </Button>
      </div>
    </div>;
};
export default ContentList;