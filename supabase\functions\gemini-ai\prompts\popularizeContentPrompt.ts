

export const popularizeContentPrompt = (content: string): string => {
  return `将以下专业内容转换为通俗易懂的科普文案，重点围绕用户选择的争议焦点进行深入阐述，直接输出科普内容，不要添加任何开场白、结尾语或说明性文字：

原始内容：
${content}

写作要求：
1. **重点突出用户选择的争议焦点**：对选中的每个争议点进行详细解读和分析
2. 使用通俗易懂的语言，将法律术语转换为日常用语
3. 针对每个选中的争议焦点，分别说明：
   - 争议的核心问题是什么
   - 各方的观点和理由
   - 法院是如何判断和认定的
   - 这个判决的意义和影响
4. 结构清晰，为每个重点争议焦点设置独立段落或小标题
5. 适当使用生活化的比喻和实例帮助读者理解复杂的法律概念
6. 保持内容的准确性，不歪曲原意
7. 语言自然流畅，避免过于官方或生硬的表达
8. 重点内容要详细展开，非重点内容可以简略处理
9. 直接输出科普内容，不要任何前言后语或总结性话语

请围绕用户选择的争议要点，生成有针对性的科普文案：`;
};

