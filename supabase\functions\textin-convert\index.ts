
import { serve } from "https://deno.land/std@0.168.0/http/server.ts"
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

// 从detail数组重建markdown内容的函数
function rebuildContentFromDetail(result: any): string {
  if (!result?.detail || !Array.isArray(result.detail)) {
    console.log('No detail array found in result')
    return ''
  }

  console.log(`Rebuilding content from ${result.detail.length} detail items`)
  
  // 按page_id和paragraph_id排序
  const sortedDetails = result.detail.sort((a: any, b: any) => {
    if (a.page_id !== b.page_id) {
      return (a.page_id || 0) - (b.page_id || 0)
    }
    return (a.paragraph_id || 0) - (b.paragraph_id || 0)
  })
  
  const markdownLines: string[] = []
  let lastOutlineLevel = -1
  
  for (const item of sortedDetails) {
    if (!item.text || typeof item.text !== 'string') {
      continue
    }
    
    const text = item.text.trim()
    if (text.length === 0) {
      continue
    }
    
    // 根据outline_level确定标题级别
    const outlineLevel = item.outline_level || -1
    
    if (outlineLevel >= 0 && outlineLevel <= 6) {
      // 这是一个标题
      const headerLevel = Math.min(outlineLevel + 1, 6) // 最多6级标题
      const headerPrefix = '#'.repeat(headerLevel)
      markdownLines.push(`${headerPrefix} ${text}`)
      markdownLines.push('') // 标题后添加空行
    } else {
      // 这是普通段落
      markdownLines.push(text)
      markdownLines.push('') // 段落后添加空行
    }
    
    lastOutlineLevel = outlineLevel
  }
  
  const rebuiltMarkdown = markdownLines.join('\n').trim()
  console.log(`Rebuilt markdown from detail array, length: ${rebuiltMarkdown.length}`)
  
  return rebuiltMarkdown
}

serve(async (req) => {
  if (req.method === 'OPTIONS') {
    return new Response(null, { headers: corsHeaders })
  }

  try {
    const supabase = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? ''
    )

    // 验证用户身份
    const authHeader = req.headers.get('Authorization')
    if (!authHeader) {
      throw new Error('No authorization header')
    }

    const { data: { user }, error: userError } = await supabase.auth.getUser(
      authHeader.replace('Bearer ', '')
    )

    if (userError || !user) {
      throw new Error('Invalid user')
    }

    // 获取Textin API配置
    const textinAppId = Deno.env.get('TEXTIN_APP_ID')
    const textinSecretCode = Deno.env.get('TEXTIN_SECRET_CODE')

    console.log('Textin API credentials check:', { 
      hasAppId: !!textinAppId, 
      hasSecretCode: !!textinSecretCode,
      appIdLength: textinAppId?.length || 0
    })

    if (!textinAppId || !textinSecretCode) {
      console.error('Missing Textin API credentials:', { textinAppId: !!textinAppId, textinSecretCode: !!textinSecretCode })
      throw new Error('Textin API credentials not configured')
    }

    const formData = await req.formData()
    const file = formData.get('file') as File

    if (!file) {
      throw new Error('No file provided')
    }

    console.log('Processing file:', {
      name: file.name,
      size: file.size,
      type: file.type,
      sizeMB: (file.size / 1024 / 1024).toFixed(2)
    })

    // 检查文件大小限制（500MB，符合Textin API规范）
    if (file.size > 500 * 1024 * 1024) {
      throw new Error('文件大小超过500MB限制，请选择较小的文件')
    }

    // 增强文件格式检测
    const fileName = file.name.toLowerCase()
    const fileType = file.type.toLowerCase()
    const supportedExtensions = ['.pdf', '.doc', '.docx']
    const supportedMimeTypes = [
      'application/pdf',
      'application/msword',
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
    ]
    
    const hasValidExtension = supportedExtensions.some(ext => fileName.endsWith(ext))
    const hasValidMimeType = supportedMimeTypes.includes(fileType)
    
    console.log('File format validation:', {
      fileName,
      fileType,
      hasValidExtension,
      hasValidMimeType
    })
    
    if (!hasValidExtension && !hasValidMimeType) {
      throw new Error('文件格式不支持，仅支持PDF、DOC、DOCX格式')
    }

    // 获取文件的二进制数据
    const fileArrayBuffer = await file.arrayBuffer()
    
    console.log('Calling Textin API for file:', {
      fileName,
      arrayBufferSize: fileArrayBuffer.byteLength,
      isWordDoc: fileName.endsWith('.doc') || fileName.endsWith('.docx')
    })
    
    // 带重试机制的API调用函数
    const callTextinWithRetry = async (retryCount = 0): Promise<Response> => {
      try {
        // 使用二进制流方式调用Textin API，设置正确的Content-Type
        const response = await fetch('https://api.textin.com/ai/service/v1/pdf_to_markdown', {
          method: 'POST',
          headers: {
            'x-ti-app-id': textinAppId,
            'x-ti-secret-code': textinSecretCode,
            'Content-Type': 'application/octet-stream', // 关键：使用官方要求的Content-Type
          },
          body: fileArrayBuffer // 直接发送二进制数据，不使用FormData
        })
        
        console.log(`Textin API call attempt ${retryCount + 1}, status:`, response.status)
        return response
      } catch (error) {
        console.error(`Textin API call attempt ${retryCount + 1} failed:`, error)
        if (retryCount < 2) { // 最多重试2次
          console.log(`Retrying in ${(retryCount + 1) * 1000}ms...`)
          await new Promise(resolve => setTimeout(resolve, (retryCount + 1) * 1000))
          return callTextinWithRetry(retryCount + 1)
        }
        throw error
      }
    }
    
    const textinResponse = await callTextinWithRetry()

    console.log('Textin API response status:', textinResponse.status)

    if (!textinResponse.ok) {
      const errorText = await textinResponse.text()
      console.error('Textin API HTTP error:', textinResponse.status, errorText)
      
      // 根据HTTP状态码提供更具体的错误信息
      if (textinResponse.status === 413) {
        throw new Error('文件过大，请选择较小的文件（最大500MB）')
      } else if (textinResponse.status === 415) {
        const isWordDoc = fileName.endsWith('.doc') || fileName.endsWith('.docx')
        if (isWordDoc) {
          throw new Error('Word文档格式不兼容，请尝试：\n1. 将文档另存为较新的.docx格式\n2. 转换为PDF格式后重新上传\n3. 检查文档是否有密码保护')
        } else {
          throw new Error('文件格式不支持，请尝试转换为PDF格式后重新上传')
        }
      } else if (textinResponse.status >= 500) {
        throw new Error('文档解析服务暂时不可用，请稍后重试')
      } else {
        throw new Error(`文档解析失败，错误代码：${textinResponse.status}`)
      }
    }

    const result = await textinResponse.json()
    console.log('Textin API response code:', result.code)
    console.log('Textin API response details:', {
      total_pages: result.result?.total_page_number,
      valid_pages: result.result?.valid_page_number,
      document_type: result.result?.document_type,
      markdown_length: result.result?.markdown?.length || 0,
      detail_items: result.result?.detail?.length || 0
    })

    // 处理Textin API特定的错误码
    if (result.code !== 200) {
      let errorMessage = '文档解析失败'
      
      switch (result.code) {
        case 40425:
          errorMessage = '文档格式不兼容，请尝试以下解决方案：\n1. 将文档另存为较新的格式（如.docx或PDF）\n2. 检查文档是否有密码保护\n3. 尝试使用其他文档编辑器重新保存'
          break
        case 40401:
          errorMessage = 'API认证失败，请联系管理员'
          break
        case 40429:
          errorMessage = '请求过于频繁，请稍后重试'
          break
        case 50001:
        case 50002:
          errorMessage = '文档解析服务暂时不可用，请稍后重试'
          break
        default:
          errorMessage = `文档解析失败：${result.message || '未知错误'}`
      }
      
      throw new Error(errorMessage)
    }

    // 增强的内容提取逻辑
    let markdown = result.result?.markdown || ''
    
    // 如果直接的markdown内容不足，尝试从detail数组重建
    if (!markdown || markdown.trim().length < 100) {
      console.log('Direct markdown insufficient, attempting to rebuild from detail array')
      markdown = rebuildContentFromDetail(result.result)
    }
    
    if (!markdown || markdown.trim().length < 100) {
      const isWordDoc = fileName.endsWith('.doc') || fileName.endsWith('.docx')
      if (isWordDoc) {
        throw new Error('Word文档内容提取失败，可能原因：\n1. 文档格式过于复杂或包含大量图像\n2. 文档受到密码保护\n3. 文档格式损坏\n请尝试将文档转换为PDF格式后重新上传')
      } else {
        throw new Error('文档内容提取失败，可能是文档为空或格式损坏')
      }
    }

    console.log('Successfully extracted markdown, length:', markdown.length)

    return new Response(
      JSON.stringify({ markdown }),
      { headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
    )

  } catch (error) {
    console.error('Error in textin-convert function:', error)
    return new Response(
      JSON.stringify({ error: error.message }),
      { 
        status: 500,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
      }
    )
  }
})
