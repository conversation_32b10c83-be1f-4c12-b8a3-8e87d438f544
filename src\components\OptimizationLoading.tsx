
const OptimizationLoading = () => {
  return (
    <div className="bg-gradient-to-br from-gray-50/80 to-blue-50/30 border border-gray-200/70 rounded-2xl p-6 min-h-[320px] flex flex-col items-center justify-center backdrop-blur-sm">
      <div className="text-center">
        {/* 旋转的蓝色圆圈 */}
        <div className="w-16 h-16 mx-auto mb-6">
          <div className="w-full h-full border-4 border-blue-100 border-t-blue-500 rounded-full animate-spin"></div>
        </div>
        
        <div className="mb-4">
          <h3 className="text-lg font-medium text-gray-700 mb-2">AI正在拼命干活</h3>
          <p className="text-gray-600">汲取知识的海绵...正在分析您的文案...优化中请稍候...</p>
        </div>
        
        <div className="w-80 bg-gray-200 rounded-full h-2">
          <div className="bg-blue-500 h-2 rounded-full animate-pulse-slow" style={{ width: '70%' }}></div>
        </div>
      </div>
      
      <style>{`
        @keyframes pulse-slow {
          0%, 100% { opacity: 0.4; }
          50% { opacity: 1; }
        }
        
        .animate-pulse-slow {
          animation: pulse-slow 2s ease-in-out infinite;
        }
      `}</style>
    </div>
  );
};

export default OptimizationLoading;
