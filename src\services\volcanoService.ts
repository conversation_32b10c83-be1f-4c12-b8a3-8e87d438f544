interface VolcanoConfig {
  apiBaseUrl: string;
  authToken?: string;
}

export class VolcanoService {
  private config: VolcanoConfig;

  constructor() {
    // 环境配置，支持不同环境的API地址
    this.config = {
      apiBaseUrl: import.meta.env.VITE_VOLCANO_API_URL || 'https://your-volcano-api-gateway.com',
    };
  }

  private async callVolcanoAPI(action: string, data: any): Promise<any> {
    const authToken = localStorage.getItem('auth_token');

    console.log(`调用火山引擎API: ${action}`, {
      url: `${this.config.apiBaseUrl}/ai-service`,
      action,
      dataKeys: Object.keys(data || {}),
      hasAuthToken: !!authToken
    });

    try {
      const response = await fetch(`${this.config.apiBaseUrl}/ai-service`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': authToken ? `Bearer ${authToken}` : '',
        },
        body: JSON.stringify({
          action,
          data
        })
      });

      console.log(`API响应状态: ${response.status}`, {
        ok: response.ok,
        statusText: response.statusText,
        headers: Object.fromEntries(response.headers.entries())
      });

      if (!response.ok) {
        const errorText = await response.text();
        console.error('API错误响应:', errorText);
        throw new Error(`火山引擎API调用失败: ${response.status} ${response.statusText} - ${errorText}`);
      }

      const result = await response.json();
      console.log('API响应结果:', result);

      if (result.error) {
        throw new Error(result.error);
      }

      return result.result;
    } catch (error) {
      console.error('火山引擎API调用异常:', error);
      throw error;
    }
  }

  async checkHealth(): Promise<boolean> {
    try {
      const response = await fetch(`${this.config.apiBaseUrl}/health`, {
        method: 'GET',
      });
      console.log('健康检查响应:', response.status, response.statusText);
      return response.ok;
    } catch (error) {
      console.error('健康检查失败:', error);
      return false;
    }
  }

  hasValidApiKey(): boolean {
    // API Key在后端管理
    return true;
  }

  setApiKey(apiKey: string) {
    // 不再需要设置API Key
    console.log('API Key现在由后端管理');
  }

  async generateTopics(domain: string): Promise<string[]> {
    try {
      const topics = await this.callVolcanoAPI('generateTopics', { domain });
      return topics;
    } catch (error) {
      console.error('生成选题失败:', error);
      throw error;
    }
  }

  async optimizeContent(
    originalText: string, 
    platform: string = "", 
    style: string = ""
  ): Promise<string> {
    try {
      const optimizedText = await this.callVolcanoAPI('optimize', {
        text: originalText,
        platform,
        style
      });
      return optimizedText;
    } catch (error) {
      console.error('优化内容失败:', error);
      throw error;
    }
  }

  async generateVideoScript(
    originalText: string, 
    platform: string = "", 
    style: string = ""
  ): Promise<string> {
    try {
      const videoScript = await this.callVolcanoAPI('generateVideoScript', {
        text: originalText,
        platform,
        style
      });
      return videoScript;
    } catch (error) {
      console.error('生成短视频脚本失败:', error);
      throw error;
    }
  }

  async generateContentByDomain(domain: string): Promise<string[]> {
    try {
      const contents = await this.callVolcanoAPI('generateContent', { domain });
      return contents;
    } catch (error) {
      console.error('生成内容失败:', error);
      throw error;
    }
  }

  async generateContentByTopic(topic: string): Promise<string[]> {
    try {
      const contents = await this.callVolcanoAPI('generateContentByTopic', { topic });
      return contents;
    } catch (error) {
      console.error('生成主题内容失败:', error);
      throw error;
    }
  }

  async generateContentByTopicStream(topic: string, onContent: (content: string) => void): Promise<string> {
    try {
      // 调用简化的流式生成API，实际上是非流式的
      const content = await this.callVolcanoAPI('generateContentByTopicStream', { topic });
      
      // 模拟流式输出效果
      if (content) {
        onContent(content);
        return content;
      }
      
      throw new Error('No content generated');
    } catch (error) {
      console.error('生成流式主题内容失败:', error);
      throw error;
    }
  }

  async popularizeContent(content: string): Promise<string> {
    try {
      const popularizedText = await this.callVolcanoAPI('popularize', { content });
      return popularizedText;
    } catch (error) {
      console.error('科普化内容失败:', error);
      throw error;
    }
  }

  async extractDocumentSummary(content: string): Promise<any> {
    try {
      const summary = await this.callVolcanoAPI('extractDocumentSummary', { content });
      return summary;
    } catch (error) {
      console.error('提取文档摘要失败:', error);
      throw error;
    }
  }
}

export const volcanoService = new VolcanoService();