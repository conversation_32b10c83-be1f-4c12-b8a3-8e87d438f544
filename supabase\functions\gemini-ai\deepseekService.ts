import { 
  generateTopicsPrompt, 
  optimizeContentPrompt, 
  generateContentPrompt, 
  generateContentByTopicPrompt, 
  popularizeContentPrompt, 
  extractDocumentSummaryPrompt 
} from './prompts/index.ts'
import { parseTopics, parseGeneratedContent, cleanXiaohongshuContent, cleanAIGeneratedContent } from './textProcessing.ts'

export class DeepseekService {
  private apiKey: string

  constructor() {
    this.apiKey = Deno.env.get('DEEPSEEK_API_KEY') || ''
    if (!this.apiKey) {
      console.warn('DeepSeek API key is not configured.')
    }
  }

  async generateTopics(domain: string): Promise<string[]> {
    const prompt = generateTopicsPrompt(domain)

    try {
      const response = await fetch('https://api.deepseek.com/v1/chat/completions', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${this.apiKey}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          model: 'deepseek-chat',
          messages: [
            { role: 'user', content: prompt }
          ],
          temperature: 0.7,
          max_tokens: 4000
        }),
      })

      if (!response.ok) {
        throw new Error(`DeepSeek API error: ${response.status}`)
      }

      const data = await response.json()
      const rawText = data.choices[0]?.message?.content || ''
      return [cleanAIGeneratedContent(rawText)]
    } catch (error) {
      console.error('Error generating topics with DeepSeek:', error)
      throw new Error('Failed to generate topics')
    }
  }

  async optimizeContent(originalText: string, platform: string = "", style: string = ""): Promise<string> {
    const prompt = optimizeContentPrompt(originalText, platform, style)

    try {
      const response = await fetch('https://api.deepseek.com/v1/chat/completions', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${this.apiKey}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          model: 'deepseek-chat',
          messages: [
            { role: 'user', content: prompt }
          ],
          temperature: 0.7,
          max_tokens: 4000
        }),
      })

      if (!response.ok) {
        throw new Error(`DeepSeek API error: ${response.status}`)
      }

      const data = await response.json()
      const rawText = data.choices[0]?.message?.content || ''
      return cleanAIGeneratedContent(rawText)
    } catch (error) {
      console.error('Error optimizing content with DeepSeek:', error)
      throw new Error('Failed to optimize content')
    }
  }

  async generateContentByDomain(domain: string): Promise<string[]> {
    const prompt = generateContentPrompt(domain)

    try {
      const response = await fetch('https://api.deepseek.com/v1/chat/completions', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${this.apiKey}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          model: 'deepseek-chat',
          messages: [
            { role: 'user', content: prompt }
          ],
          temperature: 0.7,
          max_tokens: 4000
        }),
      })

      if (!response.ok) {
        throw new Error(`DeepSeek API error: ${response.status}`)
      }

      const data = await response.json()
      const rawText = data.choices[0]?.message?.content || ''
      return [cleanAIGeneratedContent(rawText)]
    } catch (error) {
      console.error('Error generating content by domain with DeepSeek:', error)
      throw new Error('Failed to generate content by domain')
    }
  }

  async generateContentByTopic(topic: string): Promise<string[]> {
    try {
      const prompt = generateContentByTopicPrompt(topic)
      console.log('Generating content for topic:', topic)

      const response = await fetch('https://api.deepseek.com/chat/completions', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${this.apiKey}`,
        },
        body: JSON.stringify({
          model: 'deepseek-chat',
          messages: [
            {
              role: 'user',
              content: prompt
            }
          ],
          max_tokens: 2000,
          temperature: 0.7,
        }),
      })

      if (!response.ok) {
        throw new Error(`Deepseek API error: ${response.status}`)
      }

      const data = await response.json()
      const rawText = data.choices?.[0]?.message?.content || ''
      console.log('Raw response for topic content:', rawText.substring(0, 200) + '...')
      
      // 对于小红书文案，使用专门的清理函数
      const cleanedContent = cleanXiaohongshuContent(rawText)
      console.log('Cleaned xiaohongshu content, length:', cleanedContent.length)
      
      // 返回单条文案内容
      return [cleanedContent]
    } catch (error) {
      console.error('Error generating content by topic:', error)
      throw new Error(`Failed to generate content by topic: ${error.message}`)
    }
  }

  async popularizeContent(content: string): Promise<string> {
    const prompt = popularizeContentPrompt(content)
    
    try {
      const response = await fetch('https://api.deepseek.com/v1/chat/completions', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${this.apiKey}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          model: 'deepseek-chat',
          messages: [
            { role: 'user', content: prompt }
          ],
          temperature: 0.7,
          max_tokens: 4000
        }),
      })

      if (!response.ok) {
        throw new Error(`DeepSeek API error: ${response.status}`)
      }

      const data = await response.json()
      const rawText = data.choices[0]?.message?.content || ''
      return cleanAIGeneratedContent(rawText)
    } catch (error) {
      console.error('Error popularizing content with DeepSeek:', error)
      throw new Error('Failed to popularize content')
    }
  }
}
