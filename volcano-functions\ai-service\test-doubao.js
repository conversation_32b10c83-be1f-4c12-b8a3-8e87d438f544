const DoubaoService = require('./doubaoService');

async function testDoubaoAPI() {
  console.log('开始测试豆包API...');
  
  try {
    const doubaoService = new DoubaoService();
    
    // 测试生成选题
    console.log('\n=== 测试生成选题 ===');
    const topics = await doubaoService.generateTopics('法律');
    console.log('生成的选题:', topics);
    
    // 测试内容优化
    console.log('\n=== 测试内容优化 ===');
    const optimized = await doubaoService.optimizeContent(
      '这是一段测试文本，需要优化为小红书风格。',
      'xiaohongshu',
      'xiaohongshu'
    );
    console.log('优化后的内容:', optimized);
    
    console.log('\n✅ 豆包API测试成功！');
    
  } catch (error) {
    console.error('\n❌ 豆包API测试失败:', error.message);
    console.error('错误详情:', error);
  }
}

// 运行测试
testDoubaoAPI();
