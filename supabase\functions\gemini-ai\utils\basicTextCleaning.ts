
// Basic text cleaning utilities
export function removeSystemPrompts(text: string): string {
  const unwantedPatterns = [
    /好的[，,]?\s*请你?根据.{0,50}系统提示词.{0,100}为我?撰写.{0,50}/gi,
    /好的[，,]?\s*我已经理解了.{0,100}/gi,
    /好的[，,]?\s*明白了.{0,50}/gi,
    /好的[，,]?\s*我将根据.{0,50}/gi,
    /请看.{0,20}根据.{0,50}生成的.{0,50}/gi,
    /\*\*用户输入[：:]\*\*[\s\S]*?\*\*AI输出[：:]?\*\*/gi,
    /用户输入[：:]\s*`选题[：:].+?`/gi,
    /AI输出示例?.{0,20}/gi,
    /\*\*示例[：:]?\*\*/gi,
    /### \*\*AI输出[：:]?\*\*/gi,
    /请直接输出.{0,50}小红书文案/gi,
    /现在[，,]?\s*请你?提供.{0,50}选题/gi,
    /\*\*AI输出:\*\*/gi,
    /```[\s\S]*?```/g,
    /^---+$/gm,
  ];

  let cleanedText = text.trim();
  unwantedPatterns.forEach(pattern => {
    cleanedText = cleanedText.replace(pattern, '');
  });

  return cleanedText;
}

export function removeSpecialSymbols(text: string): string {
  return text
    .replace(/[【】\[\]]/g, '')
    .replace(/[《》<>]/g, '')
    .replace(/[-=]{3,}/g, '')
    .replace(/\*{2,}/g, '')
    .trim();
}

export function normalizeWhitespace(text: string): string {
  return text
    .replace(/\n{3,}/g, '\n\n')
    .replace(/[ \t]{2,}/g, ' ')
    .replace(/^\s+/gm, '')
    .trim();
}
