
import { Button } from "@/components/ui/button";
import { Sparkles } from "lucide-react";

interface EmptyStateProps {
  currentDomainInfo: {
    icon: string;
    title: string;
  };
  onGenerateTopics: () => void;
}

const EmptyState = ({ currentDomainInfo, onGenerateTopics }: EmptyStateProps) => {
  return (
    <div className="flex flex-col items-center justify-center py-20">
      <div className="bg-white/80 backdrop-blur-md rounded-3xl p-12 max-w-md mx-auto shadow-xl border border-white/50">
        <div className="text-6xl mb-6 text-center">{currentDomainInfo.icon}</div>
        <h3 className="text-xl font-semibold text-gray-900 mb-3 text-center">准备生成选题</h3>
        <p className="text-gray-600 mb-8 text-center">点击下方按钮开始生成{currentDomainInfo.title}领域的热门选题</p>
        <div className="text-center">
          <Button 
            onClick={onGenerateTopics} 
            size="lg" 
            className="bg-gradient-to-br from-blue-500 to-blue-700 hover:from-blue-600 hover:to-blue-800 text-white px-8 py-3 rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300"
          >
            <Sparkles className="w-5 h-5 mr-2" />
            生成热门选题
          </Button>
        </div>
      </div>
    </div>
  );
};

export default EmptyState;
