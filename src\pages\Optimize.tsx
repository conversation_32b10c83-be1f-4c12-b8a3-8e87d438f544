
import { useAuth } from "@/contexts/AuthContext";
import Header from "@/components/layout/Header";
import OptimizationTab from "@/components/optimization/OptimizationTab";
import { useOptimizePageState } from "@/hooks/useOptimizePageState";
import { useOptimizeLocalStorage } from "@/hooks/useOptimizeLocalStorage";
import { useOptimizeLogic } from "@/hooks/useOptimizeLogic";

const Optimize = () => {
  const { user } = useAuth();
  const { state, actions, stateRef } = useOptimizePageState();
  const { saveToLocalStorage } = useOptimizeLocalStorage(state, actions, stateRef);
  const {
    handleOptimize,
    handleCopyOptimizedText,
    handleOptimizedTextChange,
    handleVideoScriptChange
  } = useOptimizeLogic(state, actions, stateRef, saveToLocalStorage);

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-indigo-50">
      <Header />

      <div className="max-w-6xl mx-auto px-6 md:px-6 py-8">
        <OptimizationTab
          originalText={state.originalText}
          optimizedText={state.optimizedText}
          videoScript={state.videoScript}
          platform={state.platform}
          style={state.style}
          contentTypes={state.contentTypes}
          isOptimizing={state.isOptimizing}
          hasGenerated={state.hasGenerated}
          onOriginalTextChange={actions.setOriginalText}
          onPlatformChange={actions.setPlatform}
          onStyleChange={actions.setStyle}
          onContentTypesChange={actions.setContentTypes}
          onOptimize={handleOptimize}
          onCopyOptimizedText={handleCopyOptimizedText}
          onOptimizedTextChange={handleOptimizedTextChange}
          onVideoScriptChange={handleVideoScriptChange}
        />
      </div>
    </div>
  );
};

export default Optimize;
