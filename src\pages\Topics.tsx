import { useState, useEffect } from "react";
import { useParams } from "react-router-dom";
import { toast } from "@/hooks/use-toast";
import { geminiService } from "@/services/geminiService";
import { useAuth } from "@/contexts/AuthContext";
import { useGenerationHistory } from "@/hooks/useGenerationHistory";
import TopicsHeader from "@/components/topics/TopicsHeader";
import AnimatedLoadingList from "@/components/AnimatedLoadingList";
import TopicsList from "@/components/topics/TopicsList";
import EmptyState from "@/components/topics/EmptyState";
import StreamingContentCard from "@/components/topics/StreamingContentCard";
import { Button } from "@/components/ui/button";
import { ArrowLeft } from "lucide-react";

const Topics = () => {
  const { domain } = useParams<{ domain: string }>();
  const [topics, setTopics] = useState<string[]>([]);
  const [isGenerating, setIsGenerating] = useState(false);
  const [isGeneratingContent, setIsGeneratingContent] = useState(false);
  const [selectedTopics, setSelectedTopics] = useState<string[]>([]);
  const [generatedContent, setGeneratedContent] = useState<string[]>([]);
  const [streamingContent, setStreamingContent] = useState<string[]>([]);
  const [currentGeneratingIndex, setCurrentGeneratingIndex] = useState(-1);
  const [currentDomainInfo, setCurrentDomainInfo] = useState<any>(null);
  const { user } = useAuth();
  const { saveHistory } = useGenerationHistory();

  const domainMap: Record<string, any> = {
    marriage: { title: "婚姻家庭", tag: "婚姻财产", icon: "💝" },
    labor: { title: "劳动用工", tag: "劳动法律", icon: "💼" },
    debt: { title: "债务纠纷", tag: "债务追讨", icon: "💳" },
    property: { title: "房产纠纷", tag: "房产法律", icon: "🏠" },
    corporate: { title: "公司法务", tag: "公司法律", icon: "🏢" },
    consumer: { title: "消费维权", tag: "消费者权益", icon: "🛡️" },
    criminal: { title: "刑事辩护", tag: "刑事法律", icon: "⚖️" },
    contract: { title: "合同审查", tag: "合同法律", icon: "📄" }
  };

  useEffect(() => {
    if (domain && domainMap[domain]) {
      setCurrentDomainInfo(domainMap[domain]);
      generateTopics();
    }
  }, [domain]);

  const generateTopics = async () => {
    if (!domain) return;
    
    setIsGenerating(true);
    setTopics([]);
    
    try {
      const generatedTopics = await geminiService.generateTopics(domain);
      setTopics(generatedTopics);
      toast({
        title: "选题生成完成",
        description: `已为您生成${generatedTopics.length}个热门选题`,
      });
    } catch (error) {
      console.error('生成选题失败:', error);
      toast({
        title: "生成失败",
        description: error instanceof Error ? error.message : "生成过程中出现错误",
        variant: "destructive",
      });
    } finally {
      setIsGenerating(false);
    }
  };

  const handleGenerateSelectedTopics = async (selectedTopics: string[]) => {
    setSelectedTopics(selectedTopics);
    setIsGeneratingContent(true);
    setGeneratedContent([]);
    setStreamingContent(new Array(selectedTopics.length).fill(''));
    setCurrentGeneratingIndex(0);
    
    try {
      const allContents: string[] = [];
      
      // 为每个选中的选题生成一条文案，使用流式输出
      for (let i = 0; i < selectedTopics.length; i++) {
        setCurrentGeneratingIndex(i);
        const topic = selectedTopics[i];
        
        let fullContent = '';
        await geminiService.generateContentByTopicStream(topic, (content) => {
          fullContent = content;
          setStreamingContent(prev => {
            const newContent = [...prev];
            newContent[i] = content;
            return newContent;
          });
        });
        
        allContents.push(fullContent);
        
        // 保存历史记录
        if (domain && user) {
          try {
            await saveHistory(domain, topic, fullContent);
          } catch (error) {
            console.error(`保存第 ${i + 1} 条选题历史记录失败:`, error);
          }
        }
      }
      
      setGeneratedContent(allContents);
      setCurrentGeneratingIndex(-1);
      
      toast({
        title: "文案生成完成",
        description: `已为${selectedTopics.length}个选题生成专业文案`,
      });
    } catch (error) {
      console.error('生成文案失败:', error);
      toast({
        title: "生成失败",
        description: error instanceof Error ? error.message : "生成过程中出现错误",
        variant: "destructive",
      });
    } finally {
      setIsGeneratingContent(false);
      setCurrentGeneratingIndex(-1);
    }
  };

  const handleBackToTopics = () => {
    setGeneratedContent([]);
    setSelectedTopics([]);
    setStreamingContent([]);
    setCurrentGeneratingIndex(-1);
  };

  if (!currentDomainInfo) {
    return <div>加载中...</div>;
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50/70 via-white to-indigo-50/70">
      <TopicsHeader currentDomainInfo={currentDomainInfo} domain={domain} />

      <div className="max-w-6xl mx-auto px-6 py-12">
        {isGenerating ? (
          <AnimatedLoadingList
            title={`正在生成${currentDomainInfo.title}热门选题...`}
            description={`AI正在为您分析${currentDomainInfo.title}领域的热门话题`}
          />
        ) : isGeneratingContent || generatedContent.length > 0 ? (
          <div className="space-y-8">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
              {selectedTopics.map((_, index) => (
                <StreamingContentCard
                  key={index}
                  content={streamingContent[index] || generatedContent[index] || ''}
                  index={index}
                  currentDomainInfo={currentDomainInfo}
                  isGenerating={isGeneratingContent && currentGeneratingIndex === index}
                />
              ))}
            </div>

            <div className="flex flex-col items-center space-y-4 pt-8">
              <Button 
                onClick={handleBackToTopics} 
                variant="outline" 
                size="lg" 
                className="px-8 py-3 text-lg rounded-full shadow-lg hover:shadow-xl transition-all duration-300 backdrop-blur-sm border-gray-200/70 hover:border-blue-300 hover:bg-blue-50/50"
              >
                <ArrowLeft className="w-5 h-5 mr-2" />
                返回选题列表
              </Button>
            </div>
          </div>
        ) : topics.length > 0 ? (
          <TopicsList
            topics={topics}
            currentDomainInfo={currentDomainInfo}
            domain={domain!}
            onGenerateSelectedTopics={handleGenerateSelectedTopics}
          />
        ) : (
          <EmptyState
            currentDomainInfo={currentDomainInfo}
            onGenerateTopics={generateTopics}
          />
        )}
      </div>
    </div>
  );
};

export default Topics;
