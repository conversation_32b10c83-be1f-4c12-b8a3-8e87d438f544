
import React from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Copy, FileText, Sparkles } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';

interface ContentDisplayCardProps {
  title: string;
  content: string;
  filename: string;
}

export const ContentDisplayCard: React.FC<ContentDisplayCardProps> = ({
  title,
  content,
  filename
}) => {
  const { toast } = useToast();

  if (!content) return null;

  const isPopularized = title === "科普文案";

  const handleCopy = async () => {
    try {
      await navigator.clipboard.writeText(content);
      toast({
        title: "复制成功",
        description: `${title}已复制到剪贴板`
      });
    } catch (error) {
      console.error('复制失败:', error);
      toast({
        title: "复制失败",
        description: "无法复制到剪贴板，请手动选择文本复制",
        variant: "destructive"
      });
    }
  };

  return (
    <Card className="bg-white/80 backdrop-blur-sm shadow-lg border-0 shadow-blue-100/50">
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <div className={`p-2 rounded-lg ${isPopularized 
              ? 'bg-gradient-to-br from-green-500 to-green-600' 
              : 'bg-gradient-to-br from-gray-500 to-gray-600'
            }`}>
              {isPopularized ? (
                <Sparkles className="w-5 h-5 text-white" />
              ) : (
                <FileText className="w-5 h-5 text-white" />
              )}
            </div>
            <span className="text-xl">{title}</span>
          </div>
          <div className="flex items-center space-x-3">
            <span className="text-sm text-gray-500 bg-gray-100 px-3 py-1 rounded-full">
              {content.length.toLocaleString()} 字符
            </span>
            <Button
              variant="outline"
              size="sm"
              onClick={handleCopy}
              className="group relative overflow-hidden bg-gradient-to-r from-blue-50 to-blue-100 border-2 border-blue-200 hover:border-blue-300 text-blue-700 hover:text-blue-800 rounded-2xl px-6 py-2 transition-all duration-300 hover:shadow-lg hover:shadow-blue-200/50 hover:scale-105"
            >
              <div className="absolute inset-0 bg-gradient-to-r from-blue-100 to-blue-200 opacity-0 group-hover:opacity-100 transition-opacity duration-300 rounded-2xl"></div>
              <div className="relative flex items-center space-x-2">
                <Copy className="w-4 h-4 transition-transform group-hover:scale-110" />
                <span className="font-medium">复制</span>
              </div>
            </Button>
          </div>
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="max-h-96 overflow-y-auto bg-gradient-to-br from-gray-50 to-white border border-gray-200 p-6 rounded-xl">
          <div className="whitespace-pre-wrap text-sm text-gray-700 leading-relaxed space-y-4">
            {content.split('\n\n').map((paragraph, index) => (
              <p key={index} className="text-justify">
                {paragraph.trim()}
              </p>
            ))}
          </div>
        </div>
      </CardContent>
    </Card>
  );
};
