
import { textinService } from '@/services/textinService';
import { aiService } from '@/services/aiServiceFactory';
import { saveDocumentConversion } from '@/services/documentHistoryService';
import { cleanDisplayText } from '@/utils/textCleaning';
import { DocumentSummary } from '@/types/documentConvert';

export const processDocument = async (file: File): Promise<{ content: string; summary: DocumentSummary }> => {
  // 使用Textin API解析文档
  console.log('开始使用Textin API解析文档...');
  const markdown = await textinService.convertToMarkdown(file);
  console.log('Textin API解析成功，内容长度:', markdown.length);
  
  const cleanedOriginal = cleanDisplayText(markdown);

  // 使用AI服务提取文档摘要
  console.log('开始使用AI服务提取文档摘要...');
  const rawSummary = await aiService.extractDocumentSummary(markdown);
  console.log('Gemini摘要提取结果:', rawSummary);
  
  if (rawSummary.error) {
    console.error('文档摘要提取失败:', rawSummary.error);
    throw new Error(rawSummary.error + (rawSummary.suggestion ? ` ${rawSummary.suggestion}` : ''));
  }
  
  // 处理新的结构化数据格式
  const summary: DocumentSummary = {
    structured_disputes: rawSummary.structured_disputes || [],
    raw_summary: rawSummary.raw_summary || '',
    // 保持向后兼容
    案件基本情况: rawSummary.story || rawSummary.案件基本情况 || '',
    核心争议焦点: rawSummary.legal_analysis || rawSummary.核心争议焦点 || '',
    法院认定与判决: rawSummary.social_impact || rawSummary.法院认定与判决 || ''
  };

  // 验证数据有效性
  const hasStructuredData = summary.structured_disputes && summary.structured_disputes.length > 0;
  const hasLegacyData = (summary.案件基本情况 && summary.案件基本情况.trim().length > 10) ||
                       (summary.核心争议焦点 && summary.核心争议焦点.trim().length > 10) ||
                       (summary.法院认定与判决 && summary.法院认定与判决.trim().length > 10);
  const hasRawSummary = summary.raw_summary && summary.raw_summary.trim().length > 50;
  
  if (!hasStructuredData && !hasLegacyData && !hasRawSummary) {
    console.error('文档摘要数据内容不足，原始数据:', rawSummary);
    console.error('处理后数据:', summary);
    throw new Error('文档内容过少或AI无法提取有效信息，请确认文档是否包含足够的案件信息');
  }

  console.log('文档摘要验证通过，结构化争议点数:', summary.structured_disputes?.length || 0);

  return { content: cleanedOriginal, summary };
};

export const generatePopularizedContent = async (
  documentSummary: DocumentSummary,
  selectedSections: string[],
  originalContent: string,
  file: File
): Promise<string> => {
  // 构建内容用于科普化
  let contentToPopularize = `原始文档内容摘要：\n\n${originalContent.substring(0, 2000)}...\n\n重点内容部分：\n\n`;
  
  // 处理新的结构化格式
  if (documentSummary.structured_disputes && documentSummary.structured_disputes.length > 0) {
    selectedSections.forEach((sectionKey, index) => {
      // 解析争议点ID
      const disputeMatch = sectionKey.match(/^dispute_(\d+)$/);
      if (disputeMatch) {
        const disputeId = parseInt(disputeMatch[1]);
        const dispute = documentSummary.structured_disputes?.find(d => d.序号 === disputeId);
        if (dispute) {
          contentToPopularize += `${index + 1}. ${dispute.争议类型} - ${dispute.争议描述}\n裁判要点：${dispute.裁判要点}\n\n`;
        }
      } else if (sectionKey === 'raw_content' && documentSummary.raw_summary) {
        contentToPopularize += `${index + 1}. 文档核心内容：\n${documentSummary.raw_summary}\n\n`;
      }
    });
  } else {
    // 处理旧格式（向后兼容）
    selectedSections.forEach((sectionKey, index) => {
      const sectionContent = documentSummary[sectionKey as keyof DocumentSummary];
      if (sectionContent && typeof sectionContent === 'string') {
        const sectionTitles = {
          '案件基本情况': '案件基本情况与起因',
          '核心争议焦点': '核心争议焦点', 
          '法院认定与判决': '法院的关键认定与判决理由'
        };
        
        contentToPopularize += `${index + 1}. ${sectionTitles[sectionKey as keyof typeof sectionTitles] || sectionKey}：\n${sectionContent}\n\n`;
      }
    });
  }

  // 使用AI服务将内容转换为科普文案
  const popularized = await aiService.popularizeContent(contentToPopularize);
  const cleanedPopularized = cleanDisplayText(popularized);

  // 保存到数据库
  try {
    await saveDocumentConversion(file.name, originalContent, cleanedPopularized);
    console.log('文档转换结果已保存到数据库');
  } catch (saveError) {
    console.error('保存到数据库失败，但转换成功:', saveError);
  }

  return cleanedPopularized;
};
