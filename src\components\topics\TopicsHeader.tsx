
import { useNavigate } from "react-router-dom";
import { Scale, ArrowLeft } from "lucide-react";
import GenerationHistoryPanel from "../generation/GenerationHistoryPanel";

interface TopicsHeaderProps {
  currentDomainInfo: {
    icon: string;
    title: string;
  };
  domain?: string;
}

const TopicsHeader = ({ currentDomainInfo, domain }: TopicsHeaderProps) => {
  const navigate = useNavigate();

  return (
    <div className="bg-white/80 backdrop-blur-sm border-b border-gray-200/50 shadow-sm">
      <div className="max-w-6xl mx-auto px-6 py-6">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <div className="bg-gradient-to-br from-blue-500 to-blue-700 p-3 rounded-2xl shadow-lg">
              <Scale className="h-7 w-7 text-white" />
            </div>
            <div>
              <h1 className="text-2xl font-bold text-gray-900">
                {currentDomainInfo.icon} {currentDomainInfo.title} - 热门选题
              </h1>
              <p className="text-sm text-gray-600 mt-1">自媒体爆款选题生成</p>
            </div>
          </div>
          <div className="flex items-center space-x-4">
            <GenerationHistoryPanel domain={domain} />
            <button
              onClick={() => navigate("/domains")}
              className="flex items-center space-x-2 text-gray-600 hover:text-gray-900 transition-all duration-200 px-4 py-2 rounded-xl hover:bg-gray-100/60 backdrop-blur-sm"
            >
              <ArrowLeft className="w-4 h-4" />
              <span>返回领域选择</span>
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default TopicsHeader;
